# <!=Copyright (C) 2025 Rising Bamboo
# This file is distributed under the GNU General Public License v2 or later.=!>
msgid ""
msgstr ""
"Project-Id-Version: Onxen 1.1.2\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/theme/onxen\n"
"POT-Creation-Date: 2025-08-10 00:53:08+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2025-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"X-Generator: node-wp-i18n 1.2.8\n"

#: comments.php:33 comments.php:35
#: template-parts/contents/content-search.php:89
#: template-parts/contents/content-search.php:92
#: template-parts/contents/layouts/category/default.php:83
#: template-parts/contents/layouts/category/default.php:86
#: template-parts/contents/layouts/category/style-1.php:84
#: template-parts/contents/layouts/category/style-1.php:87
msgid " Comments"
msgstr ""

#: comments.php:54
msgid "Comments are closed."
msgstr ""

#: header.php:32
msgid "Skip to content"
msgstr ""

#: inc/app/class-setup.php:103
msgid "View your website"
msgstr ""

#: inc/app/class-setup.php:106
msgid "Import demo data"
msgstr ""

#: inc/app/class-setup.php:289
msgid "Start replace url"
msgstr ""

#: inc/app/class-setup.php:292
msgid "Import file path : "
msgstr ""

#: inc/app/class-setup.php:300
msgid "Urls : "
msgstr ""

#: inc/app/class-setup.php:319
msgid "Sql replace : "
msgstr ""

#: inc/app/class-setup.php:322 inc/app/class-setup.php:339
msgid "dbDelta result : "
msgstr ""

#: inc/app/class-setup.php:345
msgid "End replace url"
msgstr ""

#: inc/app/class-setup.php:377
msgid "Update term count"
msgstr ""

#: inc/app/class-setup.php:390
msgid "Rows affected"
msgstr ""

#: inc/app/class-setup.php:391
msgid "End update term count"
msgstr ""

#: inc/app/class-setup.php:402
msgid "Update comment count"
msgstr ""

#: inc/app/class-setup.php:408
msgid "Update for ID : "
msgstr ""

#: inc/app/class-setup.php:410
msgid "# End update comment count"
msgstr ""

#: inc/app/class-setup.php:423
msgid "Update Woocommerce Default Pages"
msgstr ""

#: inc/app/class-setup.php:436
msgid "Update Shop Page"
msgstr ""

#: inc/app/class-setup.php:450
msgid "Update Cart Page"
msgstr ""

#: inc/app/class-setup.php:465
msgid "Update Checkout Page"
msgstr ""

#: inc/app/class-setup.php:479
msgid "Update Account Page"
msgstr ""

#: inc/app/class-setup.php:482
msgid "Not update woocommerce pages"
msgstr ""

#: inc/app/class-setup.php:504
msgid "Start update product lookup tables"
msgstr ""

#: inc/app/class-setup.php:517
msgid "Update woocommerce product attribute type"
msgstr ""

#: inc/app/class-setup.php:521
msgid "Result : "
msgstr ""

#: inc/app/class-setup.php:523
msgid "Clear all cache : "
msgstr ""

#: inc/app/class-setup.php:525
msgid "Delete woocommerce attribute taxonomies transient : "
msgstr ""

#: inc/app/class-setup.php:526
msgid "# End update woocommerce product attribute type"
msgstr ""

#: inc/app/class-setup.php:543
msgid "Create missing woocommerce pages"
msgstr ""

#: inc/app/class-setup.php:575
msgid "Log remapped term"
msgstr ""

#: inc/app/menu/class-menu.php:79
msgid ""
"Please assign a menu to the primary location : Appearance > Menus > Manage "
"Locations"
msgstr ""

#: inc/app/menu/class-menu.php:133 inc/app/menu/class-menu.php:168
#: inc/app/menu/class-menu.php:204
msgid ""
"Please assign a menu to the account location : Appearance > Menus > Manage "
"Locations"
msgstr ""

#: inc/config/theme-customizer-default.php:428
msgid "Guarantee safe & secure checkout"
msgstr ""

#: inc/config/theme-customizer-default.php:435
msgid "Ask a Question"
msgstr ""

#: inc/config/theme-menu-location.php:12
msgid "Primary Menu"
msgstr ""

#: inc/config/theme-menu-location.php:13
msgid "Account Menu"
msgstr ""

#: inc/config/theme-menu-location.php:14
msgid "Customer Account Menu"
msgstr ""

#: inc/config/theme-menu-location.php:15
msgid "Customer Care Menu"
msgstr ""

#: inc/config/theme-plugin-required.php:10
msgid "Elementor"
msgstr ""

#: inc/config/theme-plugin-required.php:16
msgid "Rising Bamboo Core"
msgstr ""

#: inc/config/theme-plugin-required.php:26
msgid "One Click Demo Import"
msgstr ""

#: inc/config/theme-plugin-required.php:31
msgid "WooCommerce"
msgstr ""

#: inc/config/theme-plugin-required.php:36
msgid "WPC Smart Wishlist for WooCommerce"
msgstr ""

#: inc/config/theme-plugin-required.php:41
msgid "WPC Smart Compare for WooCommerce"
msgstr ""

#: inc/config/theme-plugin-required.php:46
msgid "Contact Form 7"
msgstr ""

#: inc/config/theme-setup-wizard-import.php:11
msgid "Essential"
msgstr ""

#: inc/config/theme-setup-wizard-import.php:18
msgid "Extra Data ( Post, Product, Menu ...)"
msgstr ""

#: inc/config/theme-setup-wizard.php:27
msgid "Theme Setup"
msgstr ""

#: inc/config/theme-setup-wizard.php:30
#. translators: 1: Title Tag 2: Theme Name 3: Closing Title Tag
msgid "%1$s%2$s Themes &lsaquo; Theme Setup: %3$s%4$s"
msgstr ""

#: inc/config/theme-setup-wizard.php:31
msgid "Return to the dashboard"
msgstr ""

#: inc/config/theme-setup-wizard.php:32
msgid "Disable this wizard"
msgstr ""

#: inc/config/theme-setup-wizard.php:34
msgid "Skip"
msgstr ""

#: inc/config/theme-setup-wizard.php:35 woocommerce/myaccount/orders.php:105
msgid "Next"
msgstr ""

#: inc/config/theme-setup-wizard.php:36
msgid "Start"
msgstr ""

#: inc/config/theme-setup-wizard.php:37
msgid "Cancel"
msgstr ""

#: inc/config/theme-setup-wizard.php:38 inc/config/theme-setup-wizard.php:39
#: inc/config/theme-setup-wizard.php:40
#: inc/tgm/class-tgm-plugin-activation.php:2867
msgid "Install"
msgstr ""

#: inc/config/theme-setup-wizard.php:41
msgid "Import"
msgstr ""

#: inc/config/theme-setup-wizard.php:42
#: inc/tgm/class-tgm-plugin-activation.php:2876
msgid "Activate"
msgstr ""

#: inc/config/theme-setup-wizard.php:43
msgid "Later"
msgstr ""

#: inc/config/theme-setup-wizard.php:46
#. translators: Theme Name
msgid "Activate %s"
msgstr ""

#: inc/config/theme-setup-wizard.php:48
#. translators: Theme Name
msgid "%s is Activated"
msgstr ""

#: inc/config/theme-setup-wizard.php:50
#. translators: Theme Name
msgid "Enter your license key to enable remote updates and theme support."
msgstr ""

#: inc/config/theme-setup-wizard.php:51
msgid "License key"
msgstr ""

#: inc/config/theme-setup-wizard.php:52
msgid "The theme is already registered, so you can go to the next step!"
msgstr ""

#: inc/config/theme-setup-wizard.php:53
msgid "Your theme is activated! Remote updates and theme support are enabled."
msgstr ""

#: inc/config/theme-setup-wizard.php:54
msgid "Need help?"
msgstr ""

#: inc/config/theme-setup-wizard.php:57
#. translators: Theme Name
msgid "Welcome to %s"
msgstr ""

#: inc/config/theme-setup-wizard.php:58
msgid "Hi. Welcome back"
msgstr ""

#: inc/config/theme-setup-wizard.php:59
msgid ""
"This wizard will set up your theme, install plugins, and import content. It "
"is optional & should take only a few minutes."
msgstr ""

#: inc/config/theme-setup-wizard.php:60
msgid ""
"You may have already run this theme setup wizard. If you would like to "
"proceed anyway, click on the \"Start\" button below."
msgstr ""

#: inc/config/theme-setup-wizard.php:62
msgid "Install Child Theme"
msgstr ""

#: inc/config/theme-setup-wizard.php:63
msgid "You're good to go!"
msgstr ""

#: inc/config/theme-setup-wizard.php:64
msgid "Let's build & activate a child theme so you may easily make theme changes."
msgstr ""

#: inc/config/theme-setup-wizard.php:65
msgid ""
"Your child theme has already been installed and is now activated, if it "
"wasn't already."
msgstr ""

#: inc/config/theme-setup-wizard.php:66
msgid "Learn about child themes"
msgstr ""

#: inc/config/theme-setup-wizard.php:67
msgid "Awesome. Your child theme has already been installed and is now activated."
msgstr ""

#: inc/config/theme-setup-wizard.php:68
msgid "Awesome. Your child theme has been created and is now activated."
msgstr ""

#: inc/config/theme-setup-wizard.php:70
#: inc/tgm/class-tgm-plugin-activation.php:341
msgid "Install Plugins"
msgstr ""

#: inc/config/theme-setup-wizard.php:71
msgid "You're up to speed!"
msgstr ""

#: inc/config/theme-setup-wizard.php:72
msgid "Let's install some essential WordPress plugins to get your site up to speed."
msgstr ""

#: inc/config/theme-setup-wizard.php:73
msgid ""
"The required WordPress plugins are all installed and up to date. Press "
"\"Next\" to continue the setup wizard."
msgstr ""

#: inc/config/theme-setup-wizard.php:74 inc/config/theme-setup-wizard.php:78
#: inc/customizer/panels/panels.php:54
msgid "Advanced"
msgstr ""

#: inc/config/theme-setup-wizard.php:76
msgid "Import Content"
msgstr ""

#: inc/config/theme-setup-wizard.php:77
msgid ""
"When creating a website from scratch, you should import \"Extra Data\"; "
"otherwise, import only \"Essential Data\"."
msgstr ""

#: inc/config/theme-setup-wizard.php:80
msgid "All done. Have fun!"
msgstr ""

#: inc/config/theme-setup-wizard.php:83
#. translators: Theme Author
msgid "Your theme has been all set up. Enjoy your new theme by %s."
msgstr ""

#: inc/config/theme-setup-wizard.php:84
msgid "Extras"
msgstr ""

#: inc/config/theme-setup-wizard.php:85
msgid "Explore WordPress"
msgstr ""

#: inc/config/theme-setup-wizard.php:86
msgid "Get Theme Support"
msgstr ""

#: inc/config/theme-setup-wizard.php:87
msgid "Start Customizing"
msgstr ""

#: inc/config/theme-sidebars.php:12
msgid "Top Bar"
msgstr ""

#: inc/config/theme-sidebars.php:15 inc/config/theme-sidebars.php:25
#: inc/config/theme-sidebars.php:35 inc/config/theme-sidebars.php:55
msgid "Add widgets here."
msgstr ""

#: inc/config/theme-sidebars.php:22
msgid "Sidebar Blog Top"
msgstr ""

#: inc/config/theme-sidebars.php:32
msgid "Sidebar Blog"
msgstr ""

#: inc/config/theme-sidebars.php:42
msgid "Sidebar Shop Filter"
msgstr ""

#: inc/config/theme-sidebars.php:45
msgid "Add shop filter widgets here."
msgstr ""

#: inc/config/theme-sidebars.php:52
msgid "Product Category Bottom"
msgstr ""

#: inc/customizer/panels/panels.php:18 inc/customizer/sections/blog.php:93
#: inc/customizer/sections/blog.php:228 inc/customizer/sections/general.php:36
#: inc/customizer/sections/logo.php:32
msgid "General"
msgstr ""

#: inc/customizer/panels/panels.php:19
msgid "General theme settings"
msgstr ""

#: inc/customizer/panels/panels.php:30 inc/customizer/sections/blog.php:31
#: inc/customizer/sections/blog.php:42 inc/customizer/sections/blog.php:163
#: inc/customizer/sections/blog.php:174 inc/customizer/sections/footer.php:33
#: inc/customizer/sections/footer.php:47
#: inc/customizer/sections/woocommerce.php:162
#: inc/customizer/sections/woocommerce.php:1215
#: inc/customizer/sections/woocommerce.php:1245
msgid "Layout"
msgstr ""

#: inc/customizer/panels/panels.php:31
msgid "The layout configuration"
msgstr ""

#: inc/customizer/panels/panels.php:42
msgid "Components"
msgstr ""

#: inc/customizer/panels/panels.php:43
msgid "Other components"
msgstr ""

#: inc/customizer/panels/panels.php:55
msgid "Advanced Configuration"
msgstr ""

#: inc/customizer/panels/panels.php:66 inc/helper/class-tag.php:521
msgid "Blog"
msgstr ""

#: inc/customizer/panels/panels.php:67
msgid "Blog Configuration"
msgstr ""

#: inc/customizer/sections/account.php:18
#: inc/customizer/sections/woocommerce.php:19
#: template-parts/components/mobile-navigation.php:65
#: template-parts/headers/default.php:103
#: template-parts/headers/default.php:134
#: template-parts/headers/header-04.php:113
#: template-parts/headers/header-04.php:144
msgid "Account"
msgstr ""

#: inc/customizer/sections/account.php:20
msgid "This section contains advanced all configurations for Account."
msgstr ""

#: inc/customizer/sections/account.php:31
msgid "Header Account"
msgstr ""

#: inc/customizer/sections/account.php:41 inc/customizer/sections/search.php:45
msgid "Popup"
msgstr ""

#: inc/customizer/sections/account.php:42
msgid "Show login/register form as popup."
msgstr ""

#: inc/customizer/sections/account.php:53 inc/customizer/sections/rating.php:48
#: inc/customizer/sections/scroll-to-top.php:56
#: inc/customizer/sections/search.php:142
#: inc/customizer/sections/woocommerce.php:867
msgid "Icon"
msgstr ""

#: inc/customizer/sections/account.php:54
#: inc/customizer/sections/woocommerce.php:597
#: inc/customizer/sections/woocommerce.php:798
msgid "Choose the wish list icon ?"
msgstr ""

#: inc/customizer/sections/account.php:67 inc/customizer/sections/rating.php:62
#: inc/customizer/sections/scroll-to-top.php:88
#: inc/customizer/sections/search.php:156
msgid "Icon Size"
msgstr ""

#: inc/customizer/sections/account.php:68
#: inc/customizer/sections/account.php:101
#: inc/customizer/sections/account.php:176
#: inc/customizer/sections/search.php:157
#: inc/customizer/sections/search.php:191
#: inc/customizer/sections/search.php:271
#: inc/customizer/sections/woocommerce.php:194
#: inc/customizer/sections/woocommerce.php:225
#: inc/customizer/sections/woocommerce.php:338
#: inc/customizer/sections/woocommerce.php:378
#: inc/customizer/sections/woocommerce.php:396
#: inc/customizer/sections/woocommerce.php:444
#: inc/customizer/sections/woocommerce.php:611
#: inc/customizer/sections/woocommerce.php:644
#: inc/customizer/sections/woocommerce.php:812
#: inc/customizer/sections/woocommerce.php:881
msgid "Unit : Pixel"
msgstr ""

#: inc/customizer/sections/account.php:85
#: inc/customizer/sections/scroll-to-top.php:113
#: inc/customizer/sections/search.php:174
msgid "Icon Color"
msgstr ""

#: inc/customizer/sections/account.php:100
#: inc/customizer/sections/search.php:190
msgid "Icon Border"
msgstr ""

#: inc/customizer/sections/account.php:118
#: inc/customizer/sections/search.php:224
msgid "Icon Border Radius"
msgstr ""

#: inc/customizer/sections/account.php:119
#: inc/customizer/sections/account.php:216
#: inc/customizer/sections/search.php:225
#: inc/customizer/sections/search.php:311
#: inc/customizer/sections/woocommerce.php:662
msgid ""
"Control <a target=\"_blank\" "
"href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/border-radius\"> "
"border radius</a>."
msgstr ""

#: inc/customizer/sections/account.php:138
#: inc/customizer/sections/search.php:244
msgid "Icon Border Color"
msgstr ""

#: inc/customizer/sections/account.php:160
msgid "Content Background Color"
msgstr ""

#: inc/customizer/sections/account.php:175
#: inc/customizer/sections/search.php:270
msgid "Input Border"
msgstr ""

#: inc/customizer/sections/account.php:193
#: inc/customizer/sections/search.php:288
msgid "Input Border Color"
msgstr ""

#: inc/customizer/sections/account.php:215
#: inc/customizer/sections/search.php:310
msgid "Input Border Radius"
msgstr ""

#: inc/customizer/sections/account.php:234
msgid "Show Edit Account"
msgstr ""

#: inc/customizer/sections/account.php:235
msgid "Show/Hide edit account button."
msgstr ""

#: inc/customizer/sections/account.php:248
msgid "Edit Account Icon"
msgstr ""

#: inc/customizer/sections/account.php:249
#: inc/customizer/sections/account.php:308
#: inc/customizer/sections/scroll-to-top.php:57
#: inc/customizer/sections/woocommerce.php:868
msgid "Choose the icon ?"
msgstr ""

#: inc/customizer/sections/account.php:269
msgid "Edit Account Icon Color"
msgstr ""

#: inc/customizer/sections/account.php:290
msgid "Show Logout"
msgstr ""

#: inc/customizer/sections/account.php:291
msgid "Show/Hide logout button."
msgstr ""

#: inc/customizer/sections/account.php:307
#: inc/customizer/sections/woocommerce.php:112
msgid "Logout Icon"
msgstr ""

#: inc/customizer/sections/account.php:328
msgid "Logout Icon Color"
msgstr ""

#: inc/customizer/sections/advanced.php:17
msgid "Mega Menu"
msgstr ""

#: inc/customizer/sections/advanced.php:19
msgid "This section contains advanced configurations."
msgstr ""

#: inc/customizer/sections/advanced.php:30
msgid "Normalize Classes"
msgstr ""

#: inc/customizer/sections/advanced.php:31
msgid "Remove unnecessary css classes of menu item."
msgstr ""

#: inc/customizer/sections/advanced.php:45
#: inc/customizer/sections/advanced.php:57
msgid "404 Error Page"
msgstr ""

#: inc/customizer/sections/advanced.php:47
msgid "404 Error Page Configuration."
msgstr ""

#: inc/customizer/sections/advanced.php:66
msgid "Image"
msgstr ""

#: inc/customizer/sections/advanced.php:80
#: inc/customizer/sections/promotion.php:132
#: inc/customizer/sections/woocommerce.php:536
#: inc/customizer/sections/woocommerce.php:1314
#: inc/customizer/sections/woocommerce.php:1398
msgid "Title"
msgstr ""

#: inc/customizer/sections/advanced.php:93
#: inc/customizer/sections/promotion.php:180
msgid "Description"
msgstr ""

#: inc/customizer/sections/blog.php:19
msgid "Blog Category"
msgstr ""

#: inc/customizer/sections/blog.php:21
msgid "This section contains blog category options."
msgstr ""

#: inc/customizer/sections/blog.php:45 inc/customizer/sections/blog.php:177
#: inc/customizer/sections/woocommerce.php:166
#: inc/customizer/sections/woocommerce.php:1219
msgid "Select a layout..."
msgstr ""

#: inc/customizer/sections/blog.php:56
msgid "Columns"
msgstr ""

#: inc/customizer/sections/blog.php:73 inc/customizer/sections/blog.php:188
msgid "Sidebar"
msgstr ""

#: inc/customizer/sections/blog.php:76 inc/customizer/sections/blog.php:191
msgid "Choose sidebar..."
msgstr ""

#: inc/customizer/sections/blog.php:102
msgid "Show author"
msgstr ""

#: inc/customizer/sections/blog.php:114 inc/customizer/sections/blog.php:249
msgid "Show Publish Date"
msgstr ""

#: inc/customizer/sections/blog.php:126
msgid "Show excerpt"
msgstr ""

#: inc/customizer/sections/blog.php:138
msgid "Show comment count"
msgstr ""

#: inc/customizer/sections/blog.php:150
msgid "Blog Detail"
msgstr ""

#: inc/customizer/sections/blog.php:152
msgid "This section contains blog detail options."
msgstr ""

#: inc/customizer/sections/blog.php:206
msgid "Feature Image Position"
msgstr ""

#: inc/customizer/sections/blog.php:209
#: inc/customizer/sections/woocommerce.php:1520
msgid "Select a position..."
msgstr ""

#: inc/customizer/sections/blog.php:237
msgid "Show Author"
msgstr ""

#: inc/customizer/sections/blog.php:261
msgid "Show Category"
msgstr ""

#: inc/customizer/sections/blog.php:273
msgid "Show Tag"
msgstr ""

#: inc/customizer/sections/blog.php:285
msgid "Show Comment"
msgstr ""

#: inc/customizer/sections/blog.php:297
msgid "Show Comment Form"
msgstr ""

#: inc/customizer/sections/blog.php:309
msgid "Show Social Share"
msgstr ""

#: inc/customizer/sections/blog.php:323
#: template-parts/contents/related-posts.php:57
msgid "Related Posts"
msgstr ""

#: inc/customizer/sections/blog.php:332
msgid "Show Related Posts"
msgstr ""

#: inc/customizer/sections/blog.php:344
msgid "Show Navigation"
msgstr ""

#: inc/customizer/sections/blog.php:356
msgid "Show Pagination"
msgstr ""

#: inc/customizer/sections/blog.php:368
msgid "Auto Play"
msgstr ""

#: inc/customizer/sections/blog.php:380
msgid "Auto Play Speed"
msgstr ""

#: inc/customizer/sections/footer.php:16
msgid "Footer"
msgstr ""

#: inc/customizer/sections/footer.php:17
msgid "Theme footer."
msgstr ""

#: inc/customizer/sections/footer.php:50
msgid "Select a footer..."
msgstr ""

#: inc/customizer/sections/general.php:21
msgid "Colors"
msgstr ""

#: inc/customizer/sections/general.php:22
msgid "General colors."
msgstr ""

#: inc/customizer/sections/general.php:45
msgid "Heading Color"
msgstr ""

#: inc/customizer/sections/general.php:46
msgid "H1, H2 ... H6"
msgstr ""

#: inc/customizer/sections/general.php:61
msgid "Body Text"
msgstr ""

#: inc/customizer/sections/general.php:62
msgid "Set the color for the body text."
msgstr ""

#: inc/customizer/sections/general.php:77
#: inc/customizer/sections/header.php:225
msgid "Background color"
msgstr ""

#: inc/customizer/sections/general.php:78
msgid "Visible if the grid is contained."
msgstr ""

#: inc/customizer/sections/general.php:93
msgid "Primary color"
msgstr ""

#: inc/customizer/sections/general.php:108
msgid "Secondary color"
msgstr ""

#: inc/customizer/sections/general.php:127
#: inc/customizer/sections/promotion.php:206
msgid "Link"
msgstr ""

#: inc/customizer/sections/general.php:136
msgid "Choose color"
msgstr ""

#: inc/customizer/sections/general.php:137
msgid "This is a color of the link."
msgstr ""

#: inc/customizer/sections/general.php:142
#: inc/customizer/sections/general.php:172
#: inc/customizer/sections/general.php:189
msgid "Default"
msgstr ""

#: inc/customizer/sections/general.php:143
#: inc/customizer/sections/general.php:173
#: inc/customizer/sections/general.php:190
msgid "Hover"
msgstr ""

#: inc/customizer/sections/general.php:157
msgid "General Button"
msgstr ""

#: inc/customizer/sections/general.php:166
#: inc/customizer/sections/mobile-navigation.php:73
msgid "Text Color"
msgstr ""

#: inc/customizer/sections/general.php:167
msgid "This is a color of text."
msgstr ""

#: inc/customizer/sections/general.php:183
#: inc/customizer/sections/mobile-navigation.php:51
#: inc/customizer/sections/modal.php:78
#: inc/customizer/sections/scroll-to-top.php:135
#: inc/customizer/sections/title.php:142
msgid "Background Color"
msgstr ""

#: inc/customizer/sections/general.php:184
msgid "This is a color of background button."
msgstr ""

#: inc/customizer/sections/header.php:16
msgid "Header"
msgstr ""

#: inc/customizer/sections/header.php:17
msgid "Theme header."
msgstr ""

#: inc/customizer/sections/header.php:35
msgid "Heading"
msgstr ""

#: inc/customizer/sections/header.php:44
msgid "Template"
msgstr ""

#: inc/customizer/sections/header.php:47
msgid "Select a header..."
msgstr ""

#: inc/customizer/sections/header.php:59
msgid "Navigation background color"
msgstr ""

#: inc/customizer/sections/header.php:60
msgid "Background color of the navigation."
msgstr ""

#: inc/customizer/sections/header.php:75
msgid "Login Form"
msgstr ""

#: inc/customizer/sections/header.php:76
msgid "On/Off login form in header"
msgstr ""

#: inc/customizer/sections/header.php:88 inc/customizer/sections/search.php:18
#: template-parts/components/search/overlay-form.php:29
#: template-parts/headers/default.php:78
#: template-parts/headers/header-04.php:88
msgid "Search"
msgstr ""

#: inc/customizer/sections/header.php:89
msgid "On/Off search form in header"
msgstr ""

#: inc/customizer/sections/header.php:100
msgid "Search Mobile"
msgstr ""

#: inc/customizer/sections/header.php:101
msgid "On/Off search in menu mobile"
msgstr ""

#: inc/customizer/sections/header.php:112
#: inc/customizer/sections/woocommerce.php:153
msgid "Mini Cart"
msgstr ""

#: inc/customizer/sections/header.php:113
msgid "On/Off mini cart feature in header"
msgstr ""

#: inc/customizer/sections/header.php:125
msgid "Wish List"
msgstr ""

#: inc/customizer/sections/header.php:126
msgid "On/Off wish list feature in header"
msgstr ""

#: inc/customizer/sections/header.php:144
msgid "Heading Sticky"
msgstr ""

#: inc/customizer/sections/header.php:153 inc/customizer/sections/logo.php:41
#: inc/customizer/sections/mobile-navigation.php:39
#: inc/customizer/sections/promotion.php:41
#: inc/customizer/sections/scroll-to-top.php:45
#: inc/customizer/sections/woocommerce.php:496
#: inc/customizer/sections/woocommerce.php:854
#: inc/customizer/sections/woocommerce.php:1274
#: inc/customizer/sections/woocommerce.php:1358
msgid "Enable"
msgstr ""

#: inc/customizer/sections/header.php:154
msgid "On/Off header sticky feature"
msgstr ""

#: inc/customizer/sections/header.php:166
#: inc/customizer/sections/mobile-navigation.php:95
msgid "Behaviour"
msgstr ""

#: inc/customizer/sections/header.php:167
msgid "Behaviour of header sticky when you scroll down/up the page"
msgstr ""

#: inc/customizer/sections/header.php:173
#: inc/customizer/sections/mobile-navigation.php:102
msgid "Both"
msgstr ""

#: inc/customizer/sections/header.php:174
#: inc/customizer/sections/mobile-navigation.php:103
msgid "Sticky on scroll down/up"
msgstr ""

#: inc/customizer/sections/header.php:177
#: inc/customizer/sections/mobile-navigation.php:106
msgid "Scroll Up"
msgstr ""

#: inc/customizer/sections/header.php:178
#: inc/customizer/sections/mobile-navigation.php:107
msgid "Sticky on scroll up"
msgstr ""

#: inc/customizer/sections/header.php:181
#: inc/customizer/sections/mobile-navigation.php:110
msgid "Scroll Down"
msgstr ""

#: inc/customizer/sections/header.php:182
#: inc/customizer/sections/mobile-navigation.php:111
msgid "Sticky on scroll down"
msgstr ""

#: inc/customizer/sections/header.php:200
msgid "Height"
msgstr ""

#: inc/customizer/sections/header.php:201
msgid "Height of header sticky."
msgstr ""

#: inc/customizer/sections/header.php:226
msgid "Background color of header sticky."
msgstr ""

#: inc/customizer/sections/loading.php:17
#: inc/customizer/sections/loading.php:34
msgid "Loading"
msgstr ""

#: inc/customizer/sections/loading.php:19
msgid "This section contains advanced configurations for \"Loading ...\"."
msgstr ""

#: inc/customizer/sections/loading.php:43
msgid "Block Loading"
msgstr ""

#: inc/customizer/sections/loading.php:46
#: inc/customizer/sections/loading.php:60 inc/customizer/sections/modal.php:127
msgid "Select an effect..."
msgstr ""

#: inc/customizer/sections/loading.php:57
msgid "Button Loading"
msgstr ""

#: inc/customizer/sections/logo.php:17
msgid "Logo"
msgstr ""

#: inc/customizer/sections/logo.php:18
msgid "This section contains general logo options."
msgstr ""

#: inc/customizer/sections/logo.php:42
msgid "Show/Hide the logo ?"
msgstr ""

#: inc/customizer/sections/logo.php:54
msgid "Default Logo"
msgstr ""

#: inc/customizer/sections/logo.php:55
msgid "Choose default logo."
msgstr ""

#: inc/customizer/sections/logo.php:60
msgid "Dark Logo"
msgstr ""

#: inc/customizer/sections/logo.php:61
msgid "Light Logo"
msgstr ""

#: inc/customizer/sections/logo.php:78 inc/customizer/sections/logo.php:188
msgid "Dark Version"
msgstr ""

#: inc/customizer/sections/logo.php:100 inc/customizer/sections/logo.php:210
msgid "Light Version"
msgstr ""

#: inc/customizer/sections/logo.php:122 inc/customizer/sections/logo.php:232
msgid "Logo Max Width"
msgstr ""

#: inc/customizer/sections/logo.php:146 inc/customizer/sections/logo.php:256
msgid "Logo Padding"
msgstr ""

#: inc/customizer/sections/logo.php:147 inc/customizer/sections/logo.php:257
msgid "For e.g: 1em 10rem 1vh 10px"
msgstr ""

#: inc/customizer/sections/logo.php:172
msgid "Sticky"
msgstr ""

#: inc/customizer/sections/logo.php:281
msgid "Mobile"
msgstr ""

#: inc/customizer/sections/logo.php:296
msgid "Logo Mobile Max Width"
msgstr ""

#: inc/customizer/sections/menu.php:17 inc/customizer/sections/rating.php:80
#: inc/customizer/sections/title.php:56
msgid "Color"
msgstr ""

#: inc/customizer/sections/menu.php:18
msgid "Set colors for menu."
msgstr ""

#: inc/customizer/sections/menu.php:31
msgid "Menu"
msgstr ""

#: inc/customizer/sections/menu.php:40
msgid "Menu Link"
msgstr ""

#: inc/customizer/sections/menu.php:41
msgid "Color for menu link."
msgstr ""

#: inc/customizer/sections/menu.php:56
msgid "Menu Link Hover"
msgstr ""

#: inc/customizer/sections/menu.php:57
msgid "Color for menu link when hover."
msgstr ""

#: inc/customizer/sections/menu.php:72 inc/customizer/sections/title.php:133
msgid "Background"
msgstr ""

#: inc/customizer/sections/mobile-navigation.php:17
#: inc/customizer/sections/mobile-navigation.php:30
msgid "Mobile Navigation"
msgstr ""

#: inc/customizer/sections/mobile-navigation.php:19
msgid ""
"This section contains configurations for Mobile Navigation. Please turn on "
"mobile mode when configuring."
msgstr ""

#: inc/customizer/sections/mobile-navigation.php:96
msgid "Behaviour of mobile navigation when you scroll down/up the page"
msgstr ""

#: inc/customizer/sections/modal.php:17 inc/customizer/sections/modal.php:34
msgid "Modal"
msgstr ""

#: inc/customizer/sections/modal.php:19
msgid "This section contains advanced configurations for \"Modal\"."
msgstr ""

#: inc/customizer/sections/modal.php:42
msgid "Background Blur"
msgstr ""

#: inc/customizer/sections/modal.php:53
msgid "Blur Level"
msgstr ""

#: inc/customizer/sections/modal.php:54
#: inc/customizer/sections/scroll-to-top.php:89
msgid "Unit : pixel"
msgstr ""

#: inc/customizer/sections/modal.php:100
msgid "Opacity"
msgstr ""

#: inc/customizer/sections/modal.php:124
msgid "Effect"
msgstr ""

#: inc/customizer/sections/modal.php:130
#: inc/customizer/sections/woocommerce.php:542
#: inc/customizer/sections/woocommerce.php:1320
#: inc/customizer/sections/woocommerce.php:1404
msgid "None"
msgstr ""

#: inc/customizer/sections/modal.php:131
msgid "Slide In Out Down"
msgstr ""

#: inc/customizer/sections/modal.php:132
msgid "Slide In Out Top"
msgstr ""

#: inc/customizer/sections/modal.php:133
msgid "Slide In Out Left"
msgstr ""

#: inc/customizer/sections/modal.php:134
msgid "Slide In Out Right"
msgstr ""

#: inc/customizer/sections/modal.php:135
msgid "Zoom In Out"
msgstr ""

#: inc/customizer/sections/modal.php:136
msgid "Rotate In Out Down"
msgstr ""

#: inc/customizer/sections/modal.php:137
msgid "Mix In Animations"
msgstr ""

#: inc/customizer/sections/modal.php:147
msgid "Click outside to close"
msgstr ""

#: inc/customizer/sections/modal.php:159
msgid "\"ESC\" to close"
msgstr ""

#: inc/customizer/sections/navigation.php:17
#: inc/customizer/sections/navigation.php:30
msgid "Post Navigation"
msgstr ""

#: inc/customizer/sections/navigation.php:19
msgid "This section contains advanced configurations for \"Post Navigation\"."
msgstr ""

#: inc/customizer/sections/navigation.php:39
msgid "Single Post"
msgstr ""

#: inc/customizer/sections/navigation.php:40
msgid "Display navigation for single post (or attachment, or custom post type)"
msgstr ""

#: inc/customizer/sections/navigation.php:52
msgid "Page"
msgstr ""

#: inc/customizer/sections/navigation.php:53
msgid "Display navigation for page"
msgstr ""

#: inc/customizer/sections/navigation.php:65
msgid "Attachment"
msgstr ""

#: inc/customizer/sections/navigation.php:66
msgid "Display navigation for attachment"
msgstr ""

#: inc/customizer/sections/navigation.php:78
msgid "Other Post Type"
msgstr ""

#: inc/customizer/sections/navigation.php:79
msgid "Display navigation for other post type"
msgstr ""

#: inc/customizer/sections/promotion.php:19
#: inc/customizer/sections/promotion.php:32
msgid "Promotion Popup"
msgstr ""

#: inc/customizer/sections/promotion.php:21
msgid "This section contains configurations for Promotion Popup."
msgstr ""

#: inc/customizer/sections/promotion.php:53
#: inc/tgm/class-tgm-plugin-activation.php:2697
msgid "Type"
msgstr ""

#: inc/customizer/sections/promotion.php:56
msgid "Select type..."
msgstr ""

#: inc/customizer/sections/promotion.php:59
msgid "Promotion"
msgstr ""

#: inc/customizer/sections/promotion.php:60
msgid "Newsletter"
msgstr ""

#: inc/customizer/sections/promotion.php:79
msgid "Form"
msgstr ""

#: inc/customizer/sections/promotion.php:82
msgid "Select form..."
msgstr ""

#: inc/customizer/sections/promotion.php:105
msgid "Newsletter Image"
msgstr ""

#: inc/customizer/sections/promotion.php:156
msgid "Sub Title"
msgstr ""

#: inc/customizer/sections/promotion.php:225
msgid "Promotion Image"
msgstr ""

#: inc/customizer/sections/promotion.php:252
msgid "Delay to show"
msgstr ""

#: inc/customizer/sections/promotion.php:253
msgid "Popup will be show after a number of milliseconds."
msgstr ""

#: inc/customizer/sections/promotion.php:272
msgid "Repeat"
msgstr ""

#: inc/customizer/sections/promotion.php:273
msgid "Popup will be show again after a number of minutes."
msgstr ""

#: inc/customizer/sections/promotion.php:292
msgid "Don't show again"
msgstr ""

#: inc/customizer/sections/promotion.php:311
msgid "Don't show again expried"
msgstr ""

#: inc/customizer/sections/promotion.php:312
msgid "Set a number of minutes"
msgstr ""

#: inc/customizer/sections/rating.php:22 inc/customizer/sections/rating.php:39
msgid "Rating"
msgstr ""

#: inc/customizer/sections/rating.php:24
msgid "This section contains advanced configurations for rating."
msgstr ""

#: inc/customizer/sections/rating.php:49
msgid "Choose the rating icon ?"
msgstr ""

#: inc/customizer/sections/rating.php:63
msgid "Unit : rem"
msgstr ""

#: inc/customizer/sections/rating.php:81 inc/customizer/sections/search.php:175
msgid "Change the icon color?"
msgstr ""

#: inc/customizer/sections/scroll-to-top.php:18
#: inc/customizer/sections/scroll-to-top.php:35
msgid "Scroll To Top"
msgstr ""

#: inc/customizer/sections/scroll-to-top.php:20
msgid "This section contains advanced configurations for \"Scroll To Top\"."
msgstr ""

#: inc/customizer/sections/search.php:20
msgid "This section contains advanced configurations for search."
msgstr ""

#: inc/customizer/sections/search.php:35
msgid "Feature"
msgstr ""

#: inc/customizer/sections/search.php:46
msgid "Show search component as popup."
msgstr ""

#: inc/customizer/sections/search.php:58
msgid "Search by Category"
msgstr ""

#: inc/customizer/sections/search.php:70
msgid "Ajax Search"
msgstr ""

#: inc/customizer/sections/search.php:81
msgid "Result Limit"
msgstr ""

#: inc/customizer/sections/search.php:98
msgid "Result Columns"
msgstr ""

#: inc/customizer/sections/search.php:115
msgid "Popular Keywords"
msgstr ""

#: inc/customizer/sections/search.php:116
msgid "Separated by a new line or comma."
msgstr ""

#: inc/customizer/sections/search.php:133
msgid "Style"
msgstr ""

#: inc/customizer/sections/search.php:143
msgid "Choose the search icon ?"
msgstr ""

#: inc/customizer/sections/search.php:208
msgid "Input Color"
msgstr ""

#: inc/customizer/sections/search.php:209
msgid "Change the input color ?"
msgstr ""

#: inc/customizer/sections/title.php:16 inc/customizer/sections/title.php:34
#: inc/customizer/sections/title.php:43
msgid "Page Title"
msgstr ""

#: inc/customizer/sections/title.php:17
msgid "Page Title Configuration."
msgstr ""

#: inc/customizer/sections/title.php:44
msgid "Show/Hide the page title."
msgstr ""

#: inc/customizer/sections/title.php:84 inc/customizer/sections/title.php:93
msgid "Breadcrumb"
msgstr ""

#: inc/customizer/sections/title.php:94
msgid "Show/hide the breadcrumb."
msgstr ""

#: inc/customizer/sections/title.php:106
msgid "Breadcrumb Color"
msgstr ""

#: inc/customizer/sections/title.php:157
msgid "Background Image"
msgstr ""

#: inc/customizer/sections/typography.php:36
msgid "Typography"
msgstr ""

#: inc/customizer/sections/typography.php:37
msgid "This section contains general typography options."
msgstr ""

#: inc/customizer/sections/typography.php:48
msgid "NOTICE: "
msgstr ""

#: inc/customizer/sections/typography.php:48
msgid ""
"Other typography options for specific areas can be found within other "
"sections. Example: For breadcrumb typography options go to the breadcrumb "
"section."
msgstr ""

#: inc/customizer/sections/typography.php:59
msgid "Body Typography"
msgstr ""

#: inc/customizer/sections/typography.php:68
#: inc/customizer/sections/typography.php:101
#: inc/customizer/sections/typography.php:320
#: inc/customizer/sections/typography.php:361
msgid "Font family"
msgstr ""

#: inc/customizer/sections/typography.php:69
msgid "These settings control the typography for all."
msgstr ""

#: inc/customizer/sections/typography.php:92
msgid "Heading Typography"
msgstr ""

#: inc/customizer/sections/typography.php:102
msgid "These settings control the typography for all heading text."
msgstr ""

#: inc/customizer/sections/typography.php:130
msgid "Font size"
msgstr ""

#: inc/customizer/sections/typography.php:131
msgid "H1"
msgstr ""

#: inc/customizer/sections/typography.php:156
msgid "H2"
msgstr ""

#: inc/customizer/sections/typography.php:181
msgid "H3"
msgstr ""

#: inc/customizer/sections/typography.php:206
msgid "H4"
msgstr ""

#: inc/customizer/sections/typography.php:231
msgid "H5"
msgstr ""

#: inc/customizer/sections/typography.php:256
msgid "H6"
msgstr ""

#: inc/customizer/sections/typography.php:281
msgid "Strong Tag Weight"
msgstr ""

#: inc/customizer/sections/typography.php:282
msgid "Controls font weight of &lt;strong&gt;, &lt;b&gt; tags"
msgstr ""

#: inc/customizer/sections/typography.php:288
msgid "400 - Regular"
msgstr ""

#: inc/customizer/sections/typography.php:289
msgid "500 - Medium"
msgstr ""

#: inc/customizer/sections/typography.php:290
msgid "600 - Semi Bold"
msgstr ""

#: inc/customizer/sections/typography.php:291
msgid "700 - Bold"
msgstr ""

#: inc/customizer/sections/typography.php:292
msgid "800 - Extra Bold"
msgstr ""

#: inc/customizer/sections/typography.php:293
msgid "900 - Ultra Bold (Black)"
msgstr ""

#: inc/customizer/sections/typography.php:311
msgid "Buttons"
msgstr ""

#: inc/customizer/sections/typography.php:321
msgid "These settings control the typography for buttons."
msgstr ""

#: inc/customizer/sections/typography.php:352
msgid "Form Inputs"
msgstr ""

#: inc/customizer/sections/typography.php:362
msgid "These settings control the typography for form inputs."
msgstr ""

#: inc/customizer/sections/woocommerce.php:20
msgid "Account Page Settings"
msgstr ""

#: inc/customizer/sections/woocommerce.php:33
msgid "Account Navigation"
msgstr ""

#: inc/customizer/sections/woocommerce.php:42
msgid "Dashboard Icon"
msgstr ""

#: inc/customizer/sections/woocommerce.php:43
msgid "Choose the dashboard icon ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:56
msgid "Orders Icon"
msgstr ""

#: inc/customizer/sections/woocommerce.php:57
msgid "Choose the orders icon ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:70
msgid "Downloads Icon"
msgstr ""

#: inc/customizer/sections/woocommerce.php:71
msgid "Choose the downloads icon ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:84
msgid "Addresses Icon"
msgstr ""

#: inc/customizer/sections/woocommerce.php:85
msgid "Choose the addresses icon ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:98
msgid "Details Icon"
msgstr ""

#: inc/customizer/sections/woocommerce.php:99
msgid "Choose the details icon ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:113
msgid "Choose the logout icon ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:127
#: inc/customizer/sections/woocommerce.php:141
#: woocommerce/cart/mini-cart-ajax.php:31
msgid "Cart"
msgstr ""

#: inc/customizer/sections/woocommerce.php:128
msgid "Cart & Mini Cart Settings."
msgstr ""

#: inc/customizer/sections/woocommerce.php:168
msgid "Dropdown"
msgstr ""

#: inc/customizer/sections/woocommerce.php:169
msgid "Canvas"
msgstr ""

#: inc/customizer/sections/woocommerce.php:179
msgid "Cart Icon"
msgstr ""

#: inc/customizer/sections/woocommerce.php:180
msgid "Choose the mini cart icon ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:193
msgid "Cart Size"
msgstr ""

#: inc/customizer/sections/woocommerce.php:210
msgid "Cart Icon Color"
msgstr ""

#: inc/customizer/sections/woocommerce.php:224
msgid "Cart Icon Border"
msgstr ""

#: inc/customizer/sections/woocommerce.php:241
msgid "Cart Icon Border Radius"
msgstr ""

#: inc/customizer/sections/woocommerce.php:242
msgid ""
"Control <a target=\"_blank\" "
"href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/border-radius\"> "
"border radius </a>."
msgstr ""

#: inc/customizer/sections/woocommerce.php:260
msgid "Cart Icon Border Color"
msgstr ""

#: inc/customizer/sections/woocommerce.php:281
#: inc/customizer/sections/woocommerce.php:703
msgid "Counting Text Color"
msgstr ""

#: inc/customizer/sections/woocommerce.php:295
#: inc/customizer/sections/woocommerce.php:718
msgid "Counting Background"
msgstr ""

#: inc/customizer/sections/woocommerce.php:309
#: inc/customizer/sections/woocommerce.php:733
msgid "Counting Position"
msgstr ""

#: inc/customizer/sections/woocommerce.php:322
msgid "Cart Content Background"
msgstr ""

#: inc/customizer/sections/woocommerce.php:337
msgid "Cart Content Border"
msgstr ""

#: inc/customizer/sections/woocommerce.php:355
msgid "Cart Content Border Color"
msgstr ""

#: inc/customizer/sections/woocommerce.php:377
msgid "Product Image Size"
msgstr ""

#: inc/customizer/sections/woocommerce.php:395
msgid "Remove Button Size"
msgstr ""

#: inc/customizer/sections/woocommerce.php:413
msgid "Remove Button Text Color"
msgstr ""

#: inc/customizer/sections/woocommerce.php:428
msgid "Remove Button Background"
msgstr ""

#: inc/customizer/sections/woocommerce.php:443
msgid "Remove Button Border"
msgstr ""

#: inc/customizer/sections/woocommerce.php:461
msgid "Remove Button Border Color"
msgstr ""

#: inc/customizer/sections/woocommerce.php:488
msgid "Cross-sells"
msgstr ""

#: inc/customizer/sections/woocommerce.php:507
#: inc/customizer/sections/woocommerce.php:1285
#: inc/customizer/sections/woocommerce.php:1369
msgid "Post per page"
msgstr ""

#: inc/customizer/sections/woocommerce.php:518
#: inc/customizer/sections/woocommerce.php:1296
#: inc/customizer/sections/woocommerce.php:1380
msgid "Column"
msgstr ""

#: inc/customizer/sections/woocommerce.php:529
#: inc/customizer/sections/woocommerce.php:1307
#: inc/customizer/sections/woocommerce.php:1391
msgid "Order By"
msgstr ""

#: inc/customizer/sections/woocommerce.php:533
#: inc/customizer/sections/woocommerce.php:1311
#: inc/customizer/sections/woocommerce.php:1395
msgid "Select condition..."
msgstr ""

#: inc/customizer/sections/woocommerce.php:535
#: inc/customizer/sections/woocommerce.php:1313
#: inc/customizer/sections/woocommerce.php:1397
msgid "Random"
msgstr ""

#: inc/customizer/sections/woocommerce.php:537
#: inc/customizer/sections/woocommerce.php:1315
#: inc/customizer/sections/woocommerce.php:1399
msgid "ID"
msgstr ""

#: inc/customizer/sections/woocommerce.php:538
#: inc/customizer/sections/woocommerce.php:1316
#: inc/customizer/sections/woocommerce.php:1400
#: woocommerce/myaccount/my-orders.php:17
msgid "Date"
msgstr ""

#: inc/customizer/sections/woocommerce.php:539
#: inc/customizer/sections/woocommerce.php:1317
#: inc/customizer/sections/woocommerce.php:1401
msgid "Modified"
msgstr ""

#: inc/customizer/sections/woocommerce.php:540
#: inc/customizer/sections/woocommerce.php:1318
#: inc/customizer/sections/woocommerce.php:1402
msgid "Menu order"
msgstr ""

#: inc/customizer/sections/woocommerce.php:541
#: inc/customizer/sections/woocommerce.php:1319
#: inc/customizer/sections/woocommerce.php:1403 woocommerce/cart/cart.php:33
#: woocommerce/cart/cart.php:98
msgid "Price"
msgstr ""

#: inc/customizer/sections/woocommerce.php:551
#: inc/customizer/sections/woocommerce.php:1329
#: inc/customizer/sections/woocommerce.php:1413
#: woocommerce/myaccount/my-orders.php:16
msgid "Order"
msgstr ""

#: inc/customizer/sections/woocommerce.php:555
#: inc/customizer/sections/woocommerce.php:1333
#: inc/customizer/sections/woocommerce.php:1417
msgid "Select order..."
msgstr ""

#: inc/customizer/sections/woocommerce.php:557
#: inc/customizer/sections/woocommerce.php:1335
#: inc/customizer/sections/woocommerce.php:1419
msgid "Ascending"
msgstr ""

#: inc/customizer/sections/woocommerce.php:558
#: inc/customizer/sections/woocommerce.php:1336
#: inc/customizer/sections/woocommerce.php:1420
msgid "Descending"
msgstr ""

#: inc/customizer/sections/woocommerce.php:573
#: inc/customizer/sections/woocommerce.php:587 inc/helper/class-tag.php:553
#: template-parts/components/mobile-navigation.php:52
#: template-parts/headers/default.php:157
#: template-parts/headers/header-04.php:167
msgid "Wishlist"
msgstr ""

#: inc/customizer/sections/woocommerce.php:574
msgid "Wishlist Settings."
msgstr ""

#: inc/customizer/sections/woocommerce.php:587
msgid "in header"
msgstr ""

#: inc/customizer/sections/woocommerce.php:596
#: inc/customizer/sections/woocommerce.php:756
msgid "Wishlist Icon"
msgstr ""

#: inc/customizer/sections/woocommerce.php:610
msgid "Wish List Icon Size"
msgstr ""

#: inc/customizer/sections/woocommerce.php:628
msgid "Wish List Icon Color"
msgstr ""

#: inc/customizer/sections/woocommerce.php:643
msgid "Wishlist Icon Border"
msgstr ""

#: inc/customizer/sections/woocommerce.php:661
msgid "Wishlist Icon Border Radius"
msgstr ""

#: inc/customizer/sections/woocommerce.php:681
msgid "Wishlist Icon Border Color"
msgstr ""

#: inc/customizer/sections/woocommerce.php:747
msgid "General Wishlist"
msgstr ""

#: inc/customizer/sections/woocommerce.php:757
msgid "Choose the wishlist general icon ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:774 inc/helper/class-tag.php:536
msgid "Compare"
msgstr ""

#: inc/customizer/sections/woocommerce.php:775
msgid "Compare Settings."
msgstr ""

#: inc/customizer/sections/woocommerce.php:788
msgid "General Compare"
msgstr ""

#: inc/customizer/sections/woocommerce.php:797
msgid "Compare Icon"
msgstr ""

#: inc/customizer/sections/woocommerce.php:811
msgid "Compare Icon Size"
msgstr ""

#: inc/customizer/sections/woocommerce.php:831
#: inc/customizer/sections/woocommerce.php:845 inc/helper/class-tag.php:570
msgid "Quick View"
msgstr ""

#: inc/customizer/sections/woocommerce.php:832
msgid "Quick View Settings."
msgstr ""

#: inc/customizer/sections/woocommerce.php:855
msgid "Enable/Disable the quick view ?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:880
msgid "Quick View Icon Size"
msgstr ""

#: inc/customizer/sections/woocommerce.php:899
msgid "Product Detail"
msgstr ""

#: inc/customizer/sections/woocommerce.php:900
msgid "Product Detail Settings."
msgstr ""

#: inc/customizer/sections/woocommerce.php:913
msgid "Images"
msgstr ""

#: inc/customizer/sections/woocommerce.php:924
msgid "Image layout"
msgstr ""

#: inc/customizer/sections/woocommerce.php:927
msgid "Select a image layout..."
msgstr ""

#: inc/customizer/sections/woocommerce.php:939
#: inc/customizer/sections/woocommerce.php:966
msgid "Thumbnail Position"
msgstr ""

#: inc/customizer/sections/woocommerce.php:942
#: inc/customizer/sections/woocommerce.php:969
#: inc/customizer/sections/woocommerce.php:1690
msgid "Select position..."
msgstr ""

#: inc/customizer/sections/woocommerce.php:991
#: inc/customizer/sections/woocommerce.php:1715
msgid "Thumbnail Images"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1025
msgid "Summary"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1034
msgid "Show Product Excerpt"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1046
msgid "Show Product SKU"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1058
msgid "Show Product Category"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1070
msgid "Show Product Tag"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1082
msgid "Show Sharing"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1094
msgid "Show Guarantee"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1106
msgid "Guarantee Text"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1125
msgid "Guarantee Image"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1147
#: inc/customizer/sections/woocommerce.php:1180
msgid "Contact Form"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1159
msgid "Contact Form Text"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1184
msgid "Select Contact Form..."
msgstr ""

#: inc/customizer/sections/woocommerce.php:1206
msgid "Data"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1237
msgid "Related & Upsell Layout"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1249
msgid "Select layout..."
msgstr ""

#: inc/customizer/sections/woocommerce.php:1251
msgid "Tabs"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1252
msgid "List"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1266
#: inc/woocommerce/class-woocommerce.php:174
#: woocommerce/single-product/related.php:31
msgid "Related Products"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1350
msgid "Upsells"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1438
msgid "Woocommerce"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1449
msgid "Categories per row"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1453
msgid "How many categories should be shown per row on shop or category page?"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1481
msgid "Layout Type"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1485
msgid "Select a layout type..."
msgstr ""

#: inc/customizer/sections/woocommerce.php:1487
msgid "Full Width"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1488
msgid "Container"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1498
msgid "Pagination Type"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1502
msgid "Select a pagination type..."
msgstr ""

#: inc/customizer/sections/woocommerce.php:1504
msgid "Pagination"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1505
msgid "Load More"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1506
msgid "Infinity Scroll"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1516
msgid "Filter Position"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1522
msgid "Off"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1523
msgid "Left"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1524
msgid "Right"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1525
msgid "Top"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1526
msgid "Left Canvas"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1527
msgid "Right Canvas"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1528
msgid "Top Canvas"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1529
msgid "Bottom Canvas"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1541
msgid "Product Item"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1550
msgid "Show Wishlist"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1562
msgid "Show Compare"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1574
msgid "Show Rating"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1586
msgid "Show Quick View"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1598
msgid "Show Add To Cart"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1609
msgid "Show Stock"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1620
msgid "Show Custom Fields"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1632
msgid "Enter keywords for Custom Fields"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1633
msgid ""
"keywords separated by a new line or comma. If no custom field is selected, "
"then all fields will be shown."
msgstr ""

#: inc/customizer/sections/woocommerce.php:1654
msgid "Product Images"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1655
msgid "Product Image Settings."
msgstr ""

#: inc/customizer/sections/woocommerce.php:1668
msgid "Images Hover"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1676
msgid "Enable Hover Product Images"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1687
msgid "Select Effects"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1693
msgid "Secondary Image"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1694
msgid "Zoom In"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1695
msgid "Zoom Out"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1696
msgid "Blur"
msgstr ""

#: inc/customizer/sections/woocommerce.php:1697
msgid "Shiny Slide"
msgstr ""

#: inc/helper/class-tag.php:73
#. translators: used between list items, there is a space after the comma
msgid ", "
msgstr ""

#: inc/helper/class-tag.php:76
#. translators: 1: list of categories.
msgid "Posted in %1$s"
msgstr ""

#: inc/helper/class-tag.php:83
#. translators: 1: list of tags.
msgid "Tagged %1$s"
msgstr ""

#: inc/helper/class-tag.php:93
#. translators: %s: post title
msgid "Leave a Comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr ""

#: inc/helper/class-tag.php:110 template-parts/contents/content-page.php:37
#. translators: %s: Name of current post. Only visible to screen readers
msgid "Edit <span class=\"screen-reader-text\">%s</span>"
msgstr ""

#: inc/helper/class-tag.php:261
msgid "Posts by \" "
msgstr ""

#: inc/helper/class-tag.php:261
msgid " \" "
msgstr ""

#: inc/helper/class-tag.php:263
msgid "Shop"
msgstr ""

#: inc/helper/class-tag.php:268
#. translators: Search for.
msgid "Search for: %s"
msgstr ""

#: inc/helper/class-tag.php:270
msgid "404 Error"
msgstr ""

#: inc/helper/class-tag.php:272
msgid "Knowledge Base"
msgstr ""

#: inc/helper/class-tag.php:274
msgid "Posts"
msgstr ""

#: inc/helper/class-tag.php:443
msgid "Archive by date "
msgstr ""

#: inc/helper/class-tag.php:446
msgid "Archive by month "
msgstr ""

#: inc/helper/class-tag.php:456
msgid "Archive by year "
msgstr ""

#: inc/helper/class-tag.php:458
msgid "Archive by week "
msgstr ""

#: inc/helper/class-tag.php:506
msgid "Search results for : "
msgstr ""

#: inc/helper/class-tag.php:508
msgid "Archive by tag "
msgstr ""

#: inc/helper/class-tag.php:512
msgid " Articles posted by "
msgstr ""

#: inc/helper/class-tag.php:514
msgid " Error 404 not Found "
msgstr ""

#: inc/merlin/class-merlin.php:467
msgid "Something went wrong. Please refresh the page and try again!"
msgstr ""

#: inc/merlin/class-merlin.php:615
msgid "Please define default parameters in the form of an array."
msgstr ""

#: inc/merlin/class-merlin.php:620
msgid "Please define an SVG icon filename."
msgstr ""

#: inc/merlin/class-merlin.php:743
msgid "Welcome"
msgstr ""

#: inc/merlin/class-merlin.php:750
msgid "Child"
msgstr ""

#: inc/merlin/class-merlin.php:756
msgid "License"
msgstr ""

#: inc/merlin/class-merlin.php:764
msgid "Plugins"
msgstr ""

#: inc/merlin/class-merlin.php:772 inc/merlin/class-merlin.php:2024
msgid "Content"
msgstr ""

#: inc/merlin/class-merlin.php:778
msgid "Ready"
msgstr ""

#: inc/merlin/class-merlin.php:883
msgid "The welcome step has been displayed"
msgstr ""

#: inc/merlin/class-merlin.php:977
msgid "The license activation step has been displayed"
msgstr ""

#: inc/merlin/class-merlin.php:1047
msgid "The child theme installation step has been displayed"
msgstr ""

#: inc/merlin/class-merlin.php:1134
#: inc/tgm/class-tgm-plugin-activation.php:2421
msgid "Required"
msgstr ""

#: inc/merlin/class-merlin.php:1135
msgid "req"
msgstr ""

#: inc/merlin/class-merlin.php:1175
msgid "The plugin installation step has been displayed"
msgstr ""

#: inc/merlin/class-merlin.php:1221
msgid "Select Demo"
msgstr ""

#: inc/merlin/class-merlin.php:1259
msgid "The content import step has been displayed"
msgstr ""

#: inc/merlin/class-merlin.php:1339
msgid "The final step has been displayed"
msgstr ""

#: inc/merlin/class-merlin.php:1421
msgid "The existing child theme was activated"
msgstr ""

#: inc/merlin/class-merlin.php:1438
msgid "The newly generated child theme was activated"
msgstr ""

#: inc/merlin/class-merlin.php:1453
msgid "No nonce!"
msgstr ""

#: inc/merlin/class-merlin.php:1467
msgid "Yikes! The theme activation failed. Please try again or contact support."
msgstr ""

#: inc/merlin/class-merlin.php:1476
msgid "Please add your license key before attempting to activate one."
msgstr ""

#: inc/merlin/class-merlin.php:1489
msgid "The license activation was performed with the following results"
msgstr ""

#: inc/merlin/class-merlin.php:1535 inc/merlin/class-merlin.php:1576
msgid "An error occurred, please try again."
msgstr ""

#: inc/merlin/class-merlin.php:1548
#. translators: Expiration date
msgid "Your license key expired on %s."
msgstr ""

#: inc/merlin/class-merlin.php:1554
msgid "Your license key has been disabled."
msgstr ""

#: inc/merlin/class-merlin.php:1558
msgid ""
"This appears to be an invalid license key. Please try again or contact "
"support."
msgstr ""

#: inc/merlin/class-merlin.php:1563
msgid "Your license is not active for this URL."
msgstr ""

#: inc/merlin/class-merlin.php:1568
#. translators: EDD Item Name
msgid "This appears to be an invalid license key for %s."
msgstr ""

#: inc/merlin/class-merlin.php:1572
msgid "Your license key has reached its activation limit."
msgstr ""

#: inc/merlin/class-merlin.php:1666
msgid "The child theme functions.php content was generated"
msgstr ""

#: inc/merlin/class-merlin.php:1697
msgid "The child theme style.css content was generated"
msgstr ""

#: inc/merlin/class-merlin.php:1730
msgid ""
"The child theme screenshot was copied to the child theme, with the "
"following result"
msgstr ""

#: inc/merlin/class-merlin.php:1732
msgid "The child theme screenshot was not generated, because of these results"
msgstr ""

#: inc/merlin/class-merlin.php:1762
msgid "Activating"
msgstr ""

#: inc/merlin/class-merlin.php:1778
msgid "Updating"
msgstr ""

#: inc/merlin/class-merlin.php:1794 inc/merlin/class-merlin.php:1810
#: inc/merlin/class-merlin.php:2027 inc/merlin/class-merlin.php:2040
#: inc/merlin/class-merlin.php:2053 inc/merlin/class-merlin.php:2066
#: inc/merlin/class-merlin.php:2079 inc/merlin/class-merlin.php:2092
#: inc/merlin/class-merlin.php:2133
msgid "Installing"
msgstr ""

#: inc/merlin/class-merlin.php:1802
msgid "A plugin with the following data will be processed"
msgstr ""

#: inc/merlin/class-merlin.php:1814
msgid "A plugin with the following data was processed"
msgstr ""

#: inc/merlin/class-merlin.php:1823 inc/merlin/class-merlin.php:2028
#: inc/merlin/class-merlin.php:2041 inc/merlin/class-merlin.php:2054
#: inc/merlin/class-merlin.php:2067 inc/merlin/class-merlin.php:2080
#: inc/merlin/class-merlin.php:2093
msgid "Success"
msgstr ""

#: inc/merlin/class-merlin.php:1845
msgid "The content importer AJAX call failed to start, because of incorrect data"
msgstr ""

#: inc/merlin/class-merlin.php:1850
msgid "Invalid content!"
msgstr ""

#: inc/merlin/class-merlin.php:1861
msgid "The content import AJAX call will be executed with this import data"
msgstr ""

#: inc/merlin/class-merlin.php:1903
msgid "The content import AJAX call failed with this passed data"
msgstr ""

#: inc/merlin/class-merlin.php:1914
msgid "Error"
msgstr ""

#: inc/merlin/class-merlin.php:1929
msgid ""
"The content importer AJAX call for retrieving total content import items "
"failed to start, because of incorrect data."
msgstr ""

#: inc/merlin/class-merlin.php:1933
msgid "Invalid data!"
msgstr ""

#: inc/merlin/class-merlin.php:2025
msgid "Demo content data."
msgstr ""

#: inc/merlin/class-merlin.php:2026 inc/merlin/class-merlin.php:2039
#: inc/merlin/class-merlin.php:2052 inc/merlin/class-merlin.php:2065
#: inc/merlin/class-merlin.php:2078 inc/merlin/class-merlin.php:2091
msgid "Pending"
msgstr ""

#: inc/merlin/class-merlin.php:2037
msgid "Widgets"
msgstr ""

#: inc/merlin/class-merlin.php:2038
msgid "Sample widgets data."
msgstr ""

#: inc/merlin/class-merlin.php:2050
msgid "Revolution Slider"
msgstr ""

#: inc/merlin/class-merlin.php:2051
msgid "Sample Revolution sliders data."
msgstr ""

#: inc/merlin/class-merlin.php:2063
msgid "Options"
msgstr ""

#: inc/merlin/class-merlin.php:2064
msgid "Sample theme options data."
msgstr ""

#: inc/merlin/class-merlin.php:2076
msgid "Redux Options"
msgstr ""

#: inc/merlin/class-merlin.php:2077
msgid "Redux framework options."
msgstr ""

#: inc/merlin/class-merlin.php:2089
msgid "After import setup"
msgstr ""

#: inc/merlin/class-merlin.php:2090
msgid "After import setup."
msgstr ""

#: inc/merlin/class-merlin.php:2117
msgid "The revolution slider import was executed"
msgstr ""

#: inc/merlin/class-merlin.php:2165
msgid "The Hello world post status was set to draft"
msgstr ""

#: inc/merlin/class-merlin.php:2191
msgid ""
"This predefined demo import does not have the name parameter: "
"import_file_name"
msgstr ""

#: inc/merlin/includes/class-merlin-customizer-importer.php:30
msgid "The customizer import has finished successfully"
msgstr ""

#: inc/merlin/includes/class-merlin-customizer-importer.php:57
msgid "Error: The customizer import file is missing! File path: %s"
msgstr ""

#: inc/merlin/includes/class-merlin-customizer-importer.php:70
msgid ""
"Error: The customizer import file does not have any content in it. Please "
"make sure to use the correct customizer import file."
msgstr ""

#: inc/merlin/includes/class-merlin-customizer-importer.php:80
msgid ""
"Error: The customizer import file is not in a correct format. Please make "
"sure to use the correct customizer import file."
msgstr ""

#: inc/merlin/includes/class-merlin-customizer-importer.php:86
msgid ""
"Error: The customizer import file is not suitable for current theme. You "
"can only import customizer settings for the same theme or a child theme."
msgstr ""

#: inc/merlin/includes/class-merlin-downloader.php:56
msgid "The file was not able to save to disk, while trying to download it"
msgstr ""

#: inc/merlin/includes/class-merlin-downloader.php:78
msgid "Missing URL for downloading a file!"
msgstr ""

#: inc/merlin/includes/class-merlin-downloader.php:96
msgid ""
"An error occurred while fetching file from: %1$s%2$s%3$s!%4$sReason: %5$s - "
"%6$s."
msgstr ""

#: inc/merlin/includes/class-merlin-redux-importer.php:32
msgid "The Redux Framework data was imported"
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:72
msgid "Error: Widget import file could not be found."
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:83
msgid "Error: Widget import file does not have any content in it."
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:105
msgid "Error: Widget import data could not be read. Please try a different file."
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:143
msgid "Sidebar does not exist in theme (moving widget to Inactive)"
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:164
msgid "Site does not support widget"
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:197
msgid "Widget already exists"
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:255
msgid "Imported"
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:258
msgid "Imported to Inactive"
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:264
msgid "No Title"
msgstr ""

#: inc/merlin/includes/class-merlin-widget-importer.php:326
msgid "No results for widget import!"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/Importer.php:54
msgid ""
"The XMLReader class is missing! Please install the XMLReader PHP extension "
"on your server"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/Importer.php:60
msgid "The XML file does not exists!"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/Importer.php:75
msgid "Could not open the XML file for parsing!"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/Importer.php:264
msgid "Content import start error: "
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/Importer.php:296
#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:166
#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:284
#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:368
msgid ""
"This WXR file (version %s) is newer than the importer (version %s) and may "
"not be supported. Please consider updating."
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/Importer.php:523
msgid "Time for new AJAX request!: "
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/Importer.php:528
msgid "New AJAX call!"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:128
msgid "Could not open the file for parsing"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:518
msgid "The file does not exist, please try again."
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:581
msgid "Invalid author mapping"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:682
msgid "Cannot import auto-draft posts"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:776
msgid "Failed to import \"%s\": Invalid post type %s"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:786
msgid "%s \"%s\" already exists."
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:864
msgid "Skipping attachment \"%s\", fetching attachments disabled"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:878
msgid "Failed to import \"%s\" (%s)"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:910
#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1828
msgid "Imported \"%s\" (%s)"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:915
msgid "Post %d remapped to %d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:955
msgid "Failed to import term: %s - %s"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1100
msgid "Invalid file type"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1599
msgid "Failed to import user \"%s\""
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1620
msgid "Imported user \"%s\""
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1624
msgid "User %d remapped to %d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1789
msgid "Failed to import %s %s"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1833
msgid "Term %d remapped to %d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1893
msgid "Failed to add metakey: %s, metavalue: %s to term_id: %d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1902
msgid "Meta for term_id %d : %s => %s ; successfully added!"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1954
msgid "Remote server returned %1$d %2$s for %3$s"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1975
msgid "Zero size file downloaded"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:1981
msgid "Remote file is too large, limit is %s"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2006
msgid "Running post-processing for post %d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2019
msgid "Could not find the post parent for \"%s\" (post #%d)"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2024
msgid "Post %d was imported with parent %d, but could not be found"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2038
msgid "Could not find the author for \"%s\" (post #%d)"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2043
msgid "Post %d was imported with author \"%s\", but could not be found"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2069
msgid "Post %d was marked for post-processing, but none was required."
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2080
msgid "Could not update \"%s\" (post #%d) with mapped data"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2125
msgid "Could not find the menu object for \"%s\" (post #%d)"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2130
msgid ""
"Post %d was imported with object \"%d\" of type \"%s\", but could not be "
"found"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2152
msgid "Could not find the comment parent for comment #%d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2156
msgid "Comment %d was imported with parent %d, but could not be found"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2170
msgid "Could not find the author for comment #%d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2174
msgid "Comment %d was imported with author %d, but could not be found"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2191
msgid "Could not update comment #%d with mapped data"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2222
msgid "Faulty term_id provided in terms-to-be-remapped array %s"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2232
msgid "No taxonomy provided in terms-to-be-remapped array for term #%d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2242
msgid "No parent_slug identified in remapping-array for term: %d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2250
msgid "The term(%d)\"s parent_slug (%s) is not found in the remapping-array."
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2264
msgid "No data returned by get_term_by for term_id #%d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2283
msgid "Could not update \"%s\" (term #%d) with mapped data"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2293
msgid "Term %d was successfully updated with parent %d"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2328
msgid "Starting remapping of featured images"
msgstr ""

#: inc/merlin/vendor/risingbamboo/wp-content-importer/src/WXRImporter.php:2337
msgid "Remapping featured image ID %d to new ID %d for post ID %d"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:340
msgid "Install Required Plugins"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:343
#. translators: %s: plugin name.
msgid "Installing Plugin: %s"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:345
#. translators: %s: plugin name.
msgid "Updating Plugin: %s"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:346
msgid "Something went wrong with the plugin API."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:348
#. translators: 1: plugin name(s).
msgid "This theme requires the following plugin: %1$s."
msgid_plural "This theme requires the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:354
#. translators: 1: plugin name(s).
msgid "This theme recommends the following plugin: %1$s."
msgid_plural "This theme recommends the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:360
#. translators: 1: plugin name(s).
msgid ""
"The following plugin needs to be updated to its latest version to ensure "
"maximum compatibility with this theme: %1$s."
msgid_plural ""
"The following plugins need to be updated to their latest version to ensure "
"maximum compatibility with this theme: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:366
#. translators: 1: plugin name(s).
msgid "There is an update available for: %1$s."
msgid_plural "There are updates available for the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:372
#. translators: 1: plugin name(s).
msgid "The following required plugin is currently inactive: %1$s."
msgid_plural "The following required plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:378
#. translators: 1: plugin name(s).
msgid "The following recommended plugin is currently inactive: %1$s."
msgid_plural "The following recommended plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:383
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:388
msgid "Begin updating plugin"
msgid_plural "Begin updating plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:393
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:398
msgid "Return to Required Plugins Installer"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:399
msgid "Return to the Dashboard"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:400
#: inc/tgm/class-tgm-plugin-activation.php:3311
msgid "Plugin activated successfully."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:401
#: inc/tgm/class-tgm-plugin-activation.php:3103
msgid "The following plugin was activated successfully:"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:403
#. translators: 1: plugin name.
msgid "No action taken. Plugin %1$s was already active."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:405
#. translators: 1: plugin name.
msgid ""
"Plugin not activated. A higher version of %s is needed for this theme. "
"Please update the plugin."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:407
#. translators: 1: dashboard link.
msgid "All plugins installed and activated successfully. %1$s"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:408
msgid "Dismiss this notice"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:409
msgid ""
"There are one or more required or recommended plugins to install, update or "
"activate."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:410
msgid "Please contact the administrator of this site for help."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:616
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:617
msgid "Update Required"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:735
msgid "Set the parent_slug config variable instead."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:1042
msgid ""
"The remote plugin package does not contain a folder with the desired slug "
"and renaming did not work."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:1042
#: inc/tgm/class-tgm-plugin-activation.php:1052
msgid ""
"Please contact the plugin provider and ask them to package their plugin "
"according to the WordPress guidelines."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:1052
msgid ""
"The remote plugin package consists of more than one file, but the files are "
"not packaged in a folder."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2112
#. translators: %s: version number
msgid "TGMPA v%s"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2424
#: inc/woocommerce/class-woocommerce.php:226
#: woocommerce/single-product/up-sells.php:31
msgid "Recommended"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2440
msgid "WordPress Repository"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2443
msgid "External Source"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2446
msgid "Pre-Packaged"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2463
msgid "Not Installed"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2467
msgid "Installed But Not Activated"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2469
msgid "Active"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2475
msgid "Required Update not Available"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2478
msgid "Requires Update"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2481
msgid "Update recommended"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2540
#. translators: 1: number of plugins.
msgid "To Install <span class=\"count\">(%s)</span>"
msgid_plural "To Install <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:2544
#. translators: 1: number of plugins.
msgid "Update Available <span class=\"count\">(%s)</span>"
msgid_plural "Update Available <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:2548
#. translators: 1: number of plugins.
msgid "To Activate <span class=\"count\">(%s)</span>"
msgid_plural "To Activate <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:2638
msgid "Installed version:"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2646
msgid "Minimum required version:"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2658
msgid "Available version:"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2681
msgid "No plugins to install, update or activate."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2695
msgid "Plugin"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2696
msgid "Source"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2701
msgid "Version"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2702
#: woocommerce/myaccount/my-orders.php:18
msgid "Status"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2751
#. translators: %2$s: plugin name in screen reader markup
msgid "Install %2$s"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2756
#. translators: %2$s: plugin name in screen reader markup
msgid "Update %2$s"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2762
#. translators: %2$s: plugin name in screen reader markup
msgid "Activate %2$s"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2834
msgid "Upgrade message from the plugin author:"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2873
#: woocommerce/cart/shipping-calculator.php:85
msgid "Update"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2907
msgid "No plugins were selected to be installed. No action taken."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2909
msgid "No plugins were selected to be updated. No action taken."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2950
msgid "No plugins are available to be installed at this time."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2952
msgid "No plugins are available to be updated at this time."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3059
msgid "No plugins were selected to be activated. No action taken."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3085
msgid "No plugins are available to be activated at this time."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3310
msgid "Plugin activation failed."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3655
#. translators: 1: plugin name, 2: action number 3: total number of actions.
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3658
#. translators: 1: plugin name, 2: error message.
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3660
#. translators: 1: plugin name.
msgid "The installation of %1$s failed."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3664
msgid ""
"The installation and activation process is starting. This process may take "
"a while on some hosts, so please be patient."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3666
#. translators: 1: plugin name.
msgid "%1$s installed and activated successfully."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3667
msgid "All installations and activations have been completed."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3669
#. translators: 1: plugin name, 2: action number 3: total number of actions.
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3672
msgid ""
"The installation process is starting. This process may take a while on some "
"hosts, so please be patient."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3674
#. translators: 1: plugin name.
msgid "%1$s installed successfully."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3675
msgid "All installations have been completed."
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3677
#. translators: 1: plugin name, 2: action number 3: total number of actions.
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3682
msgid "Show Details"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:3682
msgid "Hide Details"
msgstr ""

#: inc/woocommerce/class-woocommerce.php:480
#. translators: RisingBamboo.
msgid "Rated %s out of 5"
msgstr ""

#: inc/woocommerce/class-woocommerce.php:585
#. translators: %d:Review count
msgid "Reviews <span class=\"text-xs font-semibold\">(%d)</span>"
msgstr ""

#: inc/woocommerce/class-woocommerce.php:745
#: inc/woocommerce/class-woocommerce.php:751
#: inc/woocommerce/class-woocommerce.php:807
msgid "Filter By"
msgstr ""

#: searchform.php:26 searchform.php:68
msgid "No Result"
msgstr ""

#: searchform.php:36 searchform.php:79
msgid "Category"
msgstr ""

#: searchform.php:44 searchform.php:87
msgid "All Categories"
msgstr ""

#: searchform.php:55
msgid "What Are You Looking For ?"
msgstr ""

#: searchform.php:89 template-parts/breadcrumb.php:23
#: template-parts/components/mobile-navigation.php:37
msgid "Home"
msgstr ""

#: searchform.php:105 searchform.php:106
msgid "Enter Your Keyword"
msgstr ""

#: searchform.php:109
msgid "search"
msgstr ""

#: template-parts/404.php:38
msgid "Back to Homepage"
msgstr ""

#: template-parts/404.php:41
msgid "Continue shopping"
msgstr ""

#: template-parts/account/canvas-menu.php:25
#: template-parts/headers/default.php:53
#: template-parts/headers/header-02.php:53
#: template-parts/headers/header-03.php:43
#: template-parts/headers/header-04.php:41
#: template-parts/headers/header-04.php:51
#: template-parts/headers/header-05.php:41
#: template-parts/headers/header-05.php:51
#: template-parts/headers/header-06.php:43
#: template-parts/headers/header-07.php:43
msgid "logo"
msgstr ""

#: template-parts/account/canvas-menu.php:32
msgid "CUSTOMER ACCOUNT"
msgstr ""

#: template-parts/account/canvas-menu.php:40
msgid "CUSTOMER CARE"
msgstr ""

#: template-parts/account/customer-account.php:19
#: template-parts/account/logout.php:26
msgid "Edit Account"
msgstr ""

#: template-parts/account/customer-account.php:27
#: template-parts/account/logout.php:18
msgid "Logout"
msgstr ""

#: template-parts/account/customer-account.php:38
#: template-parts/account/login-form.php:15
#: template-parts/account/login-form.php:79
msgid "Register"
msgstr ""

#: template-parts/account/customer-account.php:43
#: template-parts/account/login-form.php:14
#: template-parts/account/login-form.php:45
#: template-parts/account/logout.php:37 woocommerce/global/form-login.php:56
msgid "Login"
msgstr ""

#: template-parts/account/login-form.php:21
msgid "Insert your account information:"
msgstr ""

#: template-parts/account/login-form.php:24
#: template-parts/account/login-form.php:62
#: woocommerce/myaccount/form-edit-account.php:50
msgid "Email address"
msgstr ""

#: template-parts/account/login-form.php:25
msgid "Enter your email"
msgstr ""

#: template-parts/account/login-form.php:28
#: template-parts/account/login-form.php:29
#: template-parts/account/login-form.php:67
msgid "Password"
msgstr ""

#: template-parts/account/login-form.php:36
#: woocommerce/global/form-login.php:50
msgid "Remember me"
msgstr ""

#: template-parts/account/login-form.php:40
#: woocommerce/global/form-login.php:51
msgid "Lost your password?"
msgstr ""

#: template-parts/account/login-form.php:54
msgid "Create your account:"
msgstr ""

#: template-parts/account/login-form.php:57
msgid "Username"
msgstr ""

#: template-parts/account/login-form.php:58
msgid "User name"
msgstr ""

#: template-parts/account/login-form.php:63
msgid "email"
msgstr ""

#: template-parts/account/login-form.php:68
msgid "password"
msgstr ""

#: template-parts/account/login-form.php:71
msgid "A link to set a new password will be sent to your email address."
msgstr ""

#: template-parts/archive/archive-rbb_testimonial.php:24
msgid "what's happy customers say"
msgstr ""

#: template-parts/archive/archive-rbb_testimonial.php:26
msgid "See Why Thousands of Customer Love Us!"
msgstr ""

#: template-parts/components/mobile-navigation.php:41
msgid "Shopping"
msgstr ""

#: template-parts/components/mobile-navigation.php:83
msgid "Welcome "
msgstr ""

#: template-parts/components/promotion-popup.php:55
msgid "Don't show this popup again"
msgstr ""

#: template-parts/components/search/search-result.php:17
msgid "Popular Search:"
msgstr ""

#: template-parts/contents/content-none.php:22
#. translators: 1: link to WP admin new post page.
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr ""

#: template-parts/contents/content-none.php:35
msgid ""
"Sorry, but nothing matched your search terms. Please try again with some "
"different keywords."
msgstr ""

#: template-parts/contents/content-none.php:42
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""

#: template-parts/contents/content-page.php:23
#: template-parts/contents/content-rbb_testimonial.php:55
msgid "Pages:"
msgstr ""

#: template-parts/contents/content-rbb_testimonial.php:22
#. translators: %s: Name of current post. Only visible to screen readers
msgid "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr ""

#: template-parts/contents/content-search.php:40
#: template-parts/contents/layouts/category/default.php:34
#: template-parts/contents/layouts/category/style-1.php:35
#: template-parts/contents/layouts/post/default.php:40
#: template-parts/contents/layouts/post/parts/feature-image.php:46
#: template-parts/contents/related-posts.php:134
#: template-parts/elementor/widgets/posts/fragments/item.php:40
msgid "By "
msgstr ""

#: template-parts/contents/content-search.php:87
#: template-parts/contents/layouts/category/default.php:81
#: template-parts/contents/layouts/category/style-1.php:82
msgid "Comment"
msgstr ""

#: template-parts/contents/content-search.php:102
#: template-parts/contents/layouts/category/default.php:100
#: template-parts/contents/layouts/category/style-1.php:101
#: template-parts/contents/related-posts.php:146
#: template-parts/elementor/widgets/posts/fragments/item.php:54
msgid "Read more"
msgstr ""

#: template-parts/contents/layouts/post/default.php:105
msgid "Tags :"
msgstr ""

#: template-parts/elementor/widgets/banner/banner-1.php:36
#: template-parts/elementor/widgets/banner/banner-10.php:37
#: template-parts/elementor/widgets/banner/banner-2.php:67
#: template-parts/elementor/widgets/banner/banner-3.php:66
#: template-parts/elementor/widgets/banner/banner-4.php:83
#: template-parts/elementor/widgets/banner/banner-5.php:37
#: template-parts/elementor/widgets/banner/banner-6.php:193
#: template-parts/elementor/widgets/banner/banner-7.php:42
#: template-parts/elementor/widgets/banner/banner-8.php:42
#: template-parts/elementor/widgets/banner/banner-9.php:37
#: template-parts/elementor/widgets/banner/default.php:33
#: template-parts/elementor/widgets/banner/image.php:42
#: template-parts/elementor/widgets/banner/video-2.php:89
#: template-parts/elementor/widgets/banner/video-3.php:88
#: template-parts/elementor/widgets/slider/single/default.php:75
#: template-parts/elementor/widgets/slider/single/layout-2.php:75
#: template-parts/elementor/widgets/slider/single/layout-3.php:78
#: template-parts/elementor/widgets/slider/single/layout-4.php:80
#: template-parts/elementor/widgets/slider/single/layout-5.php:77
#: template-parts/elementor/widgets/slider/single/layout-6.php:77
#: template-parts/elementor/widgets/slider/single/layout-7.php:75
#: template-parts/elementor/widgets/slider/single/layout-8.php:75
#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:98
#: template-parts/elementor/widgets/woo-products/fragments/item.php:120
#: woocommerce/content-product.php:133
msgid "Day%!H"
msgstr ""

#: template-parts/elementor/widgets/banner/banner-1.php:37
#: template-parts/elementor/widgets/banner/banner-10.php:38
#: template-parts/elementor/widgets/banner/banner-2.php:68
#: template-parts/elementor/widgets/banner/banner-3.php:67
#: template-parts/elementor/widgets/banner/banner-4.php:84
#: template-parts/elementor/widgets/banner/banner-5.php:38
#: template-parts/elementor/widgets/banner/banner-6.php:194
#: template-parts/elementor/widgets/banner/banner-7.php:43
#: template-parts/elementor/widgets/banner/banner-8.php:43
#: template-parts/elementor/widgets/banner/banner-9.php:38
#: template-parts/elementor/widgets/banner/default.php:34
#: template-parts/elementor/widgets/banner/image.php:43
#: template-parts/elementor/widgets/banner/video-2.php:90
#: template-parts/elementor/widgets/banner/video-3.php:89
#: template-parts/elementor/widgets/slider/single/default.php:77
#: template-parts/elementor/widgets/slider/single/layout-2.php:77
#: template-parts/elementor/widgets/slider/single/layout-3.php:80
#: template-parts/elementor/widgets/slider/single/layout-4.php:82
#: template-parts/elementor/widgets/slider/single/layout-5.php:79
#: template-parts/elementor/widgets/slider/single/layout-6.php:79
#: template-parts/elementor/widgets/slider/single/layout-7.php:76
#: template-parts/elementor/widgets/slider/single/layout-8.php:77
#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:99
#: template-parts/elementor/widgets/woo-products/fragments/item.php:121
#: woocommerce/content-product.php:134
msgid "Hour%!H"
msgstr ""

#: template-parts/elementor/widgets/banner/banner-1.php:38
#: template-parts/elementor/widgets/banner/banner-10.php:39
#: template-parts/elementor/widgets/banner/banner-2.php:69
#: template-parts/elementor/widgets/banner/banner-3.php:68
#: template-parts/elementor/widgets/banner/banner-4.php:85
#: template-parts/elementor/widgets/banner/banner-5.php:39
#: template-parts/elementor/widgets/banner/banner-6.php:195
#: template-parts/elementor/widgets/banner/banner-7.php:44
#: template-parts/elementor/widgets/banner/banner-8.php:44
#: template-parts/elementor/widgets/banner/banner-9.php:39
#: template-parts/elementor/widgets/banner/default.php:35
#: template-parts/elementor/widgets/banner/image.php:44
#: template-parts/elementor/widgets/banner/video-2.php:91
#: template-parts/elementor/widgets/banner/video-3.php:90
#: template-parts/elementor/widgets/slider/single/default.php:79
#: template-parts/elementor/widgets/slider/single/layout-2.php:79
#: template-parts/elementor/widgets/slider/single/layout-3.php:82
#: template-parts/elementor/widgets/slider/single/layout-4.php:84
#: template-parts/elementor/widgets/slider/single/layout-5.php:81
#: template-parts/elementor/widgets/slider/single/layout-6.php:81
#: template-parts/elementor/widgets/slider/single/layout-7.php:77
#: template-parts/elementor/widgets/slider/single/layout-8.php:79
#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:100
#: template-parts/elementor/widgets/woo-products/fragments/item.php:122
#: woocommerce/content-product.php:135
msgid "Min%!H"
msgstr ""

#: template-parts/elementor/widgets/banner/banner-1.php:39
#: template-parts/elementor/widgets/banner/banner-10.php:40
#: template-parts/elementor/widgets/banner/banner-2.php:70
#: template-parts/elementor/widgets/banner/banner-3.php:69
#: template-parts/elementor/widgets/banner/banner-4.php:86
#: template-parts/elementor/widgets/banner/banner-5.php:40
#: template-parts/elementor/widgets/banner/banner-6.php:196
#: template-parts/elementor/widgets/banner/banner-7.php:45
#: template-parts/elementor/widgets/banner/banner-8.php:45
#: template-parts/elementor/widgets/banner/banner-9.php:40
#: template-parts/elementor/widgets/banner/default.php:36
#: template-parts/elementor/widgets/banner/image.php:45
#: template-parts/elementor/widgets/banner/video-2.php:92
#: template-parts/elementor/widgets/banner/video-3.php:91
msgid "Sec"
msgstr ""

#: template-parts/elementor/widgets/banner/banner-1.php:115
#: template-parts/elementor/widgets/banner/banner-10.php:115
#: template-parts/elementor/widgets/banner/banner-2.php:145
#: template-parts/elementor/widgets/banner/banner-3.php:145
#: template-parts/elementor/widgets/banner/banner-4.php:160
#: template-parts/elementor/widgets/banner/banner-5.php:116
#: template-parts/elementor/widgets/banner/banner-6.php:145
#: template-parts/elementor/widgets/banner/banner-7.php:124
#: template-parts/elementor/widgets/banner/banner-8.php:113
#: template-parts/elementor/widgets/banner/banner-9.php:115
#: template-parts/elementor/widgets/banner/default.php:114
#: template-parts/elementor/widgets/banner/image.php:113
#: template-parts/elementor/widgets/banner/video-2.php:207
#: template-parts/elementor/widgets/banner/video-3.php:209
#: template-parts/elementor/widgets/banner/video.php:135
#: template-parts/elementor/widgets/slider/single/default.php:188
#: template-parts/elementor/widgets/slider/single/default.php:276
#: template-parts/elementor/widgets/slider/single/layout-2.php:187
#: template-parts/elementor/widgets/slider/single/layout-2.php:275
#: template-parts/elementor/widgets/slider/single/layout-3.php:190
#: template-parts/elementor/widgets/slider/single/layout-3.php:278
#: template-parts/elementor/widgets/slider/single/layout-4.php:192
#: template-parts/elementor/widgets/slider/single/layout-4.php:280
#: template-parts/elementor/widgets/slider/single/layout-5.php:189
#: template-parts/elementor/widgets/slider/single/layout-5.php:277
#: template-parts/elementor/widgets/slider/single/layout-6.php:189
#: template-parts/elementor/widgets/slider/single/layout-6.php:277
#: template-parts/elementor/widgets/slider/single/layout-7.php:175
#: template-parts/elementor/widgets/slider/single/layout-7.php:265
#: template-parts/elementor/widgets/slider/single/layout-8.php:188
#: template-parts/elementor/widgets/slider/single/layout-8.php:275
msgid "Your browser does not support the video tag."
msgstr ""

#: template-parts/elementor/widgets/posts/fragments/item.php:29
msgid "Default post thumbnail"
msgstr ""

#: template-parts/elementor/widgets/slider/single/default.php:81
#: template-parts/elementor/widgets/slider/single/layout-2.php:81
#: template-parts/elementor/widgets/slider/single/layout-3.php:84
#: template-parts/elementor/widgets/slider/single/layout-4.php:86
#: template-parts/elementor/widgets/slider/single/layout-5.php:83
#: template-parts/elementor/widgets/slider/single/layout-6.php:83
#: template-parts/elementor/widgets/slider/single/layout-7.php:78
#: template-parts/elementor/widgets/slider/single/layout-8.php:81
#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:101
#: template-parts/elementor/widgets/woo-products/fragments/item.php:123
#: woocommerce/content-product.php:136
msgid "Secs"
msgstr ""

#: template-parts/elementor/widgets/slider/single/default.php:340
#: template-parts/elementor/widgets/slider/single/layout-2.php:339
#: template-parts/elementor/widgets/slider/single/layout-3.php:342
#: template-parts/elementor/widgets/slider/single/layout-4.php:344
#: template-parts/elementor/widgets/slider/single/layout-5.php:341
#: template-parts/elementor/widgets/slider/single/layout-6.php:341
#: template-parts/elementor/widgets/slider/single/layout-7.php:329
#: template-parts/elementor/widgets/slider/single/layout-8.php:339
msgid "Image Slider "
msgstr ""

#: template-parts/elementor/widgets/woo-products/default.php:44
msgid "Of"
msgstr ""

#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:64
#: template-parts/elementor/widgets/woo-products/fragments/item.php:78
#: woocommerce/content-product.php:91
msgid "Second image of "
msgstr ""

#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:81
#: template-parts/elementor/widgets/woo-products/fragments/item.php:102
#: woocommerce/content-product.php:98
msgid "New"
msgstr ""

#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:85
#: template-parts/elementor/widgets/woo-products/fragments/item.php:106
#: woocommerce/content-product.php:102
msgid "Sold out"
msgstr ""

#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:128
#: template-parts/elementor/widgets/woo-products/fragments/item.php:171
#: woocommerce/content-product.php:214
#. translators: 1: Product stock.
msgid " %s Products in stock"
msgstr ""

#: template-parts/elementor/widgets/woo-products/fragments/item-2.php:138
#: template-parts/elementor/widgets/woo-products/fragments/item.php:182
#: template-parts/elementor/widgets/woo-products/fragments/item.php:223
#: woocommerce/content-product.php:184 woocommerce/content-product.php:225
#: woocommerce/content-product.php:273
msgid "Out of stock"
msgstr ""

#: template-parts/elementor/widgets/woo-products/fragments/item.php:177
#: woocommerce/content-product.php:220
msgid "In Stock"
msgstr ""

#: template-parts/elementor/widgets/woo-products/fragments/item.php:225
#: woocommerce/content-product.php:186 woocommerce/content-product.php:275
msgid "Select options"
msgstr ""

#: template-parts/elementor/widgets/woo-products/fragments/item.php:227
#: woocommerce/content-product.php:188 woocommerce/content-product.php:277
msgid "Add to Cart"
msgstr ""

#: template-parts/elementor/widgets/woo-single-product/fragments/image.php:23
#: woocommerce/single-product/product-image-quick-view.php:37
#: woocommerce/single-product/product-image.php:33
msgid "Placeholder"
msgstr ""

#: template-parts/elementor/widgets/woo-single-product/fragments/meta.php:32
#: woocommerce/single-product/meta.php:36
msgid "SKU : "
msgstr ""

#: template-parts/elementor/widgets/woo-single-product/fragments/meta.php:32
msgid "N/A : "
msgstr ""

#: template-parts/footers/default.php:14
msgid "https://wordpress.org/"
msgstr ""

#: template-parts/footers/default.php:17
#. translators: %s: CMS name, i.e. WordPress.
msgid "Proudly powered by %s"
msgstr ""

#: template-parts/footers/default.php:23
#. translators: 1: Theme author.
msgid "© Copyright : %1$s"
msgstr ""

#: template-parts/headers/default.php:60
#: template-parts/headers/header-02.php:60
#: template-parts/headers/header-03.php:50
#: template-parts/headers/header-04.php:57
#: template-parts/headers/header-05.php:57
#: template-parts/headers/header-06.php:50
#: template-parts/headers/header-07.php:50
msgid "Sticky Logo"
msgstr ""

#: template-parts/shortcode/social-share.php:20
msgid "Facebook"
msgstr ""

#: template-parts/shortcode/social-share.php:23
msgid "Twitter"
msgstr ""

#: template-parts/shortcode/social-share.php:26
msgid "LinkedIn"
msgstr ""

#: template-parts/shortcode/social-share.php:29
msgid "Pinterest"
msgstr ""

#: template-parts/shortcode/social-share.php:32
msgid "Tumblr"
msgstr ""

#: template-parts/shortcode/social-share.php:35
msgid "Email"
msgstr ""

#: template-parts/shortcode/social-share.php:40
msgid "Social Share"
msgstr ""

#: template-parts/shortcode/social-share.php:45
msgid "Copy link"
msgstr ""

#: template-parts/shortcode/social-share.php:49
msgid "Copy"
msgstr ""

#: template-parts/shortcode/social-share.php:51
msgid "Share"
msgstr ""

#: template-parts/shortcode/social-share.php:60
msgid "Social Share :"
msgstr ""

#: template-parts/single/single-post.php:45 template-parts/single/single.php:22
msgid "Previous:"
msgstr ""

#: template-parts/single/single-post.php:46 template-parts/single/single.php:23
msgid "Next:"
msgstr ""

#: template-parts/widgets/filter-active-bar.php:21
#: template-parts/widgets/filter-active-bar.php:48
msgid "Clear All"
msgstr ""

#: template-parts/widgets/price-filter/slider.php:26
msgid "Ranger "
msgstr ""

#: template-parts/widgets/price-filter/slider.php:35
msgid "Filter"
msgstr ""

#: woocommerce/archive-product.php:137
msgid "See More +"
msgstr ""

#: woocommerce/archive-product.php:140
msgid "See Less -"
msgstr ""

#: woocommerce/cart/cart-shipping.php:52
#. Translators: $s shipping destination.
msgid "Shipping to %s."
msgstr ""

#: woocommerce/cart/cart-shipping.php:53
msgid "Change address"
msgstr ""

#: woocommerce/cart/cart-shipping.php:55
msgid "Shipping options will be updated during checkout."
msgstr ""

#: woocommerce/cart/cart-shipping.php:63
msgid "Shipping costs are calculated during checkout."
msgstr ""

#: woocommerce/cart/cart-shipping.php:65
msgid "Enter your address to view shipping options."
msgstr ""

#: woocommerce/cart/cart-shipping.php:68
msgid ""
"There are no shipping options available. Please ensure that your address "
"has been entered correctly, or contact us if you need any help."
msgstr ""

#: woocommerce/cart/cart-shipping.php:71
#. Translators: $s shipping destination.
msgid "No shipping options were found for %s."
msgstr ""

#: woocommerce/cart/cart-shipping.php:72
msgid "Enter a different address"
msgstr ""

#: woocommerce/cart/cart-totals.php:27
msgid "Cart totals"
msgstr ""

#: woocommerce/cart/cart-totals.php:32 woocommerce/cart/cart-totals.php:33
#: woocommerce/cart/cart.php:35 woocommerce/cart/cart.php:128
#: woocommerce/checkout/review-order.php:24
#: woocommerce/checkout/review-order.php:50
msgid "Subtotal"
msgstr ""

#: woocommerce/cart/cart-totals.php:56 woocommerce/cart/cart-totals.php:57
msgid "Shipping"
msgstr ""

#: woocommerce/cart/cart-totals.php:76
#. translators: %s location.
msgid "(estimated for %s)"
msgstr ""

#: woocommerce/cart/cart-totals.php:103 woocommerce/cart/cart-totals.php:105
#: woocommerce/checkout/review-order.php:102
#: woocommerce/myaccount/my-orders.php:19
#: woocommerce/order/order-details.php:52
msgid "Total"
msgstr ""

#: woocommerce/cart/cart.php:32 woocommerce/cart/cart.php:66
#: woocommerce/cart/cart.php:77 woocommerce/checkout/review-order.php:23
#: woocommerce/order/order-details.php:51
msgid "Product"
msgstr ""

#: woocommerce/cart/cart.php:34 woocommerce/cart/cart.php:104
#: woocommerce/global/quantity-input.php:24
msgid "Quantity"
msgstr ""

#: woocommerce/cart/cart.php:57 woocommerce/cart/cart.php:141
#: woocommerce/cart/mini-cart.php:61
msgid "Remove this item"
msgstr ""

#: woocommerce/cart/cart.php:92
msgid "Available on backorder"
msgstr ""

#: woocommerce/cart/cart.php:164 woocommerce/checkout/form-coupon.php:36
msgid "Coupon code"
msgstr ""

#: woocommerce/cart/cart.php:166 woocommerce/checkout/form-coupon.php:39
msgid "Apply coupon"
msgstr ""

#: woocommerce/cart/cart.php:175
msgid "Update cart"
msgstr ""

#: woocommerce/cart/cross-sells.php:26
msgid "You May Also Like"
msgstr ""

#: woocommerce/cart/mini-cart-ajax.php:26
msgid "Product was added to cart successfully!"
msgstr ""

#: woocommerce/cart/mini-cart-ajax.php:49
msgid "Shopping Cart"
msgstr ""

#: woocommerce/cart/mini-cart.php:100
msgid "No products in the cart."
msgstr ""

#: woocommerce/cart/mini-cart.php:103
msgid "Your cart is currently empty. Let us help you find the perfect item!"
msgstr ""

#: woocommerce/cart/mini-cart.php:108
msgid "continue shopping"
msgstr ""

#: woocommerce/cart/proceed-to-checkout-button.php:27
msgid "Proceed to checkout"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:25
msgid "Calculate shipping"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:30
msgid "Select a country / region&hellip;"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:49
#: woocommerce/cart/shipping-calculator.php:54
#: woocommerce/cart/shipping-calculator.php:66
msgid "State / County"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:55
msgid "Select an option&hellip;"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:75
msgid "City"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:81
msgid "Postcode / ZIP"
msgstr ""

#: woocommerce/cart/shipping-free-goal.php:19
#. translators: 1: Product price
msgid "Buy <strong>%s</strong> more to get <strong>Free Shipping</strong>"
msgstr ""

#: woocommerce/cart/shipping-free-goal.php:23
msgid ""
"Congratulations! You’ve got<strong class=\"uppercase pl-1\">Free "
"Shipping</strong>"
msgstr ""

#: woocommerce/checkout/form-billing.php:26
msgid "Billing &amp; Shipping"
msgstr ""

#: woocommerce/checkout/form-billing.php:30
msgid "Billing details"
msgstr ""

#: woocommerce/checkout/form-billing.php:57
msgid "Create an account?"
msgstr ""

#: woocommerce/checkout/form-checkout.php:37
msgid "You must be logged in to checkout."
msgstr ""

#: woocommerce/checkout/form-checkout.php:71
msgid "Your order"
msgstr ""

#: woocommerce/checkout/form-coupon.php:30
msgid "Have a coupon?"
msgstr ""

#: woocommerce/checkout/form-coupon.php:30
msgid "Click here to enter your code"
msgstr ""

#: woocommerce/checkout/form-coupon.php:34
msgid "If you have a coupon code, please apply it below."
msgstr ""

#: woocommerce/checkout/form-login.php:30
msgid "Returning customer?"
msgstr ""

#: woocommerce/checkout/form-login.php:30
msgid "Click here to login"
msgstr ""

#: woocommerce/checkout/form-login.php:35
msgid ""
"If you have shopped with us before, please enter your details below. If you "
"are a new customer, please proceed to the Billing section."
msgstr ""

#: woocommerce/checkout/form-shipping.php:30
msgid "Ship to a different address?"
msgstr ""

#: woocommerce/checkout/form-shipping.php:60
msgid "Additional information"
msgstr ""

#: woocommerce/checkout/payment.php:36
msgid ""
"Sorry, it seems that there are no available payment methods. Please contact "
"us if you require assistance or wish to make alternate arrangements."
msgstr ""

#: woocommerce/checkout/payment.php:36
msgid "Please fill in your details above to see available payment methods."
msgstr ""

#: woocommerce/checkout/payment.php:46
#. translators: $1 and $2 opening and closing emphasis tags respectively
msgid ""
"Since your browser does not support JavaScript, or it is disabled, please "
"ensure you click the %1$sUpdate Totals%2$s button before placing your "
"order. You may be charged more than the amount stated above if you fail to "
"do so."
msgstr ""

#: woocommerce/checkout/payment.php:48
msgid "Update totals"
msgstr ""

#: woocommerce/checkout/thankyou.php:34
msgid ""
"Unfortunately your order cannot be processed as the originating "
"bank/merchant has declined your transaction. Please attempt your purchase "
"again."
msgstr ""

#: woocommerce/checkout/thankyou.php:37
msgid "Pay"
msgstr ""

#: woocommerce/checkout/thankyou.php:39
msgid "My account"
msgstr ""

#: woocommerce/checkout/thankyou.php:47 woocommerce/checkout/thankyou.php:125
msgid "Thank you. Your order has been received."
msgstr ""

#: woocommerce/checkout/thankyou.php:79
msgid "Order number:"
msgstr ""

#: woocommerce/checkout/thankyou.php:86
msgid "Date:"
msgstr ""

#: woocommerce/checkout/thankyou.php:93
msgid "Email:"
msgstr ""

#: woocommerce/checkout/thankyou.php:101
msgid "Total:"
msgstr ""

#: woocommerce/checkout/thankyou.php:109
msgid "Payment method:"
msgstr ""

#: woocommerce/content-categories-loop.php:39
#: woocommerce/content-categories-loop.php:97
msgid " Products"
msgstr ""

#: woocommerce/global/form-login.php:38
msgid "Username or email*"
msgstr ""

#: woocommerce/global/form-login.php:41
msgid "Password*"
msgstr ""

#: woocommerce/global/quantity-input.php:24
#. translators: %s: Quantity.
msgid "%s quantity"
msgstr ""

#: woocommerce/global/quantity-input.php:47
msgid "Product quantity"
msgstr ""

#: woocommerce/loop/no-products-found.php:17
msgid "no-products"
msgstr ""

#: woocommerce/loop/no-products-found.php:19
msgid "No products found"
msgstr ""

#: woocommerce/loop/no-products-found.php:20
msgid ""
"Sorry, no products matched your selection. Please try again with different "
"criteria."
msgstr ""

#: woocommerce/loop/no-products-found.php:22
msgid "Continue Shopping"
msgstr ""

#: woocommerce/loop/orderby.php:31
msgid "Shop order"
msgstr ""

#: woocommerce/loop/pagination.php:55
#. translators: %1$s: Number of products being shown. %2$s: Total number of
#. products.
msgid "Showing %1$s of %2$s products"
msgstr ""

#: woocommerce/loop/pagination.php:71
msgid "Load more items"
msgstr ""

#: woocommerce/loop/result-count.php:50
msgid "Showing the single result"
msgstr ""

#: woocommerce/loop/result-count.php:53
#. translators: %d: total results
msgid "Showing all %d result"
msgid_plural "Showing all %d results"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/myaccount/dashboard.php:38
#. translators: 1: user display name 2: logout url
msgid ""
"Hello <span class=\"font-bold\"> %1$s </span> (not <span "
"class=\"font-bold\"> %1$s? </span> <a href=\"%2$s\">Log out</a>)"
msgstr ""

#: woocommerce/myaccount/dashboard.php:48
#. translators: 1: Orders URL 2: Address URL 3: Account URL.
msgid ""
"From your account dashboard you can view your <a href=\"%1$s\">recent "
"orders</a>, manage your <a href=\"%2$s\">billing address</a>, and <a "
"href=\"%3$s\">edit your password and account details</a>."
msgstr ""

#: woocommerce/myaccount/dashboard.php:51
#. translators: 1: Orders URL 2: Addresses URL 3: Account URL.
msgid ""
"From your account dashboard you can view your <a href=\"%1$s\">recent "
"orders</a>, manage your <a href=\"%2$s\">shipping and billing "
"addresses</a>, and <a href=\"%3$s\">edit your password and account "
"details</a>."
msgstr ""

#: woocommerce/myaccount/downloads.php:43
msgid "No downloads available yet."
msgstr ""

#: woocommerce/myaccount/downloads.php:47 woocommerce/myaccount/orders.php:118
msgid "Browse products"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:24
#: woocommerce/myaccount/navigation.php:30
msgid "My Account"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:32
msgid "First name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:37
msgid "Last name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:43
msgid "Display name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:46
msgid ""
"This will be how your name will be displayed in the account section and in "
"reviews"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:62
msgid "Password change"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:64
msgid "Current password (leave blank to leave unchanged)"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:68
msgid "New password (leave blank to leave unchanged)"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:72
msgid "Confirm new password"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:81
msgid "Save changes"
msgstr ""

#: woocommerce/myaccount/form-edit-address.php:22
#: woocommerce/myaccount/my-address.php:28
#: woocommerce/myaccount/my-address.php:37
#: woocommerce/order/order-details-customer.php:28
msgid "Billing address"
msgstr ""

#: woocommerce/myaccount/form-edit-address.php:22
#: woocommerce/myaccount/my-address.php:29
#: woocommerce/order/order-details-customer.php:48
msgid "Shipping address"
msgstr ""

#: woocommerce/myaccount/form-edit-address.php:46
msgid "Save address"
msgstr ""

#: woocommerce/myaccount/my-address.php:45
msgid "The following addresses will be used on the checkout page by default."
msgstr ""

#: woocommerce/myaccount/my-address.php:60
msgid "Edit"
msgstr ""

#: woocommerce/myaccount/my-address.php:62
msgid "Add"
msgstr ""

#: woocommerce/myaccount/my-address.php:74
msgid "You have not set up this type of address yet."
msgstr ""

#: woocommerce/myaccount/my-orders.php:39
msgid "Recent orders"
msgstr ""

#: woocommerce/myaccount/my-orders.php:77 woocommerce/myaccount/orders.php:70
#. translators: 1: formatted order total 2: total order items
msgid "%1$s for %2$s item"
msgid_plural "%1$s for %2$s items"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/myaccount/orders.php:101
msgid "Previous"
msgstr ""

#: woocommerce/myaccount/orders.php:115
msgid "No order has been made yet."
msgstr ""

#: woocommerce/myaccount/view-order.php:34
#. translators: 1: order number 2: order date 3: order status
msgid "Order #%1$s was placed on %2$s and is currently %3$s."
msgstr ""

#: woocommerce/myaccount/view-order.php:44
msgid "Order updates"
msgstr ""

#: woocommerce/myaccount/view-order.php:50
msgid "l jS \\o\\f F Y, h:ia"
msgstr ""

#: woocommerce/order/order-details-customer.php:31
#: woocommerce/order/order-details-customer.php:51
#: woocommerce/single-product/meta.php:36
msgid "N/A"
msgstr ""

#: woocommerce/order/order-details.php:92
msgid "Note:"
msgstr ""

#: woocommerce/single-product/add-to-cart/buy-it-now.php:16
msgid "Buy it now"
msgstr ""

#: woocommerce/single-product/add-to-cart/grouped.php:77
msgid "Buy one of this item"
msgstr ""

#: woocommerce/single-product/ask-question.php:13
msgid "%s"
msgstr ""

#: woocommerce/single-product/guarantee.php:18
msgid "Guarantee & safe checkout"
msgstr ""

#: woocommerce/single-product/shipping-delivery-estimate.php:17
msgid "Estimated Delivery : "
msgstr ""

#: woocommerce/single-product/shipping-free-calculator.php:16
msgid "Free Shipping & Returns"
msgstr ""

#: woocommerce/single-product/shipping-free-calculator.php:18
msgid "On all order over %s"
msgstr ""

#: woocommerce/single-product/shipping-free-calculator.php:20
msgid "On all order"
msgstr ""

#: woocommerce/single-product/swatches.php:23
msgid "This product is currently out of stock and unavailable."
msgstr ""

#: woocommerce/single-product/swatches.php:117
msgid "Clear"
msgstr ""

#. Theme Name of the plugin/theme
msgid "Onxen"
msgstr ""

#. Theme URI of the plugin/theme
msgid "https://one.risingbamboo.com/"
msgstr ""

#. Description of the plugin/theme
msgid ""
"Onxen – Single Product WooCommerce theme is a versatile and modern solution "
"designed specifically for stores that focus on selling a single product,"
msgstr ""

#. Author of the plugin/theme
msgid "Rising Bamboo"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://risingbamboo.com"
msgstr ""

#: inc/helper/class-tag.php:44
#. translators: %s: post date.
msgctxt "post date"
msgid "Posted on %s"
msgstr ""

#: inc/helper/class-tag.php:58
#. translators: %s: post author.
msgctxt "post author"
msgid "by %s"
msgstr ""

#: inc/helper/class-tag.php:80
#. translators: used between list items, there is a space after the comma
msgctxt "list item separator"
msgid ", "
msgstr ""

#: inc/helper/class-tag.php:314
msgctxt "breadcrumb"
msgid "Home"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:1243
#: inc/tgm/class-tgm-plugin-activation.php:3099
msgctxt "plugin A *and* plugin B"
msgid "and"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2490
#. translators: 1: install status, 2: update status
msgctxt "Install/Update Status"
msgid "%1$s, %2$s"
msgstr ""

#: inc/tgm/class-tgm-plugin-activation.php:2536
#. translators: 1: number of plugins.
msgctxt "plugins"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/tgm/class-tgm-plugin-activation.php:2630
msgctxt "as in: \"version nr unknown\""
msgid "unknown"
msgstr ""

#: woocommerce/loop/result-count.php:58
#. translators: 1: last result 2: total results
msgctxt "with first and last result"
msgid "Showing %1$d of %2$d result"
msgid_plural "Showing %1$d of %2$d results"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/myaccount/my-orders.php:65 woocommerce/myaccount/orders.php:58
msgctxt "hash before order number"
msgid "#"
msgstr ""