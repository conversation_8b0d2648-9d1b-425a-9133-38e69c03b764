# Arabic translation for Onxen theme
# Copyright (C) 2025 Rising Bamboo
# This file is distributed under the GNU General Public License v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Onxen 1.1.2\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/theme/onxen\n"
"POT-Creation-Date: 2025-08-10 00:53:08+00:00\n"
"PO-Revision-Date: 2025-08-10 12:00:00+0000\n"
"Last-Translator: Arabic Translator <<EMAIL>>\n"
"Language-Team: Arabic <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"X-Generator: Poedit 3.0\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_n;_x;_ex;_nx;esc_attr__;esc_attr_e;esc_attr_x;esc_html__;esc_html_e;esc_html_x;_n_noop;_nx_noop\n"
"X-Poedit-SearchPath-0: .\n"

#: comments.php:33 comments.php:35
#: template-parts/contents/content-search.php:89
#: template-parts/contents/content-search.php:92
#: template-parts/contents/layouts/category/default.php:83
#: template-parts/contents/layouts/category/default.php:86
#: template-parts/contents/layouts/category/style-1.php:84
#: template-parts/contents/layouts/category/style-1.php:87
msgid " Comments"
msgstr " تعليقات"

#: comments.php:54
msgid "Comments are closed."
msgstr "التعليقات مغلقة."

#: header.php:32
msgid "Skip to content"
msgstr "انتقل إلى المحتوى"

#: inc/app/class-setup.php:103
msgid "View your website"
msgstr "عرض موقعك الإلكتروني"

#: inc/app/class-setup.php:106
msgid "Import demo data"
msgstr "استيراد البيانات التجريبية"

#: inc/app/class-setup.php:289
msgid "Start replace url"
msgstr "بدء استبدال الرابط"

#: inc/app/class-setup.php:292
msgid "Import file path : "
msgstr "مسار ملف الاستيراد : "

#: inc/app/class-setup.php:300
msgid "Urls : "
msgstr "الروابط : "

#: inc/app/class-setup.php:319
msgid "Sql replace : "
msgstr "استبدال SQL : "

#: inc/app/class-setup.php:322 inc/app/class-setup.php:339
msgid "dbDelta result : "
msgstr "نتيجة dbDelta : "

#: inc/app/class-setup.php:345
msgid "End replace url"
msgstr "انتهاء استبدال الرابط"

#: inc/app/class-setup.php:377
msgid "Update term count"
msgstr "تحديث عدد المصطلحات"

#: inc/app/class-setup.php:390
msgid "Rows affected"
msgstr "الصفوف المتأثرة"

#: inc/app/class-setup.php:391
msgid "End update term count"
msgstr "انتهاء تحديث عدد المصطلحات"

#: inc/app/class-setup.php:402
msgid "Update comment count"
msgstr "تحديث عدد التعليقات"

#: inc/app/class-setup.php:408
msgid "Update for ID : "
msgstr "تحديث للمعرف : "

#: inc/app/class-setup.php:410
msgid "# End update comment count"
msgstr "# انتهاء تحديث عدد التعليقات"

#: inc/app/class-setup.php:423
msgid "Update Woocommerce Default Pages"
msgstr "تحديث صفحات ووكومرس الافتراضية"

#: inc/app/class-setup.php:436
msgid "Update Shop Page"
msgstr "تحديث صفحة المتجر"

#: inc/app/class-setup.php:450
msgid "Update Cart Page"
msgstr "تحديث صفحة السلة"

#: inc/app/class-setup.php:465
msgid "Update Checkout Page"
msgstr "تحديث صفحة الدفع"

#: inc/app/class-setup.php:479
msgid "Update Account Page"
msgstr "تحديث صفحة الحساب"

#: inc/app/class-setup.php:482
msgid "Not update woocommerce pages"
msgstr "عدم تحديث صفحات ووكومرس"

#: inc/app/class-setup.php:504
msgid "Start update product lookup tables"
msgstr "بدء تحديث جداول البحث عن المنتجات"

#: inc/app/class-setup.php:517
msgid "Update woocommerce product attribute type"
msgstr "تحديث نوع خاصية منتج ووكومرس"

#: inc/app/class-setup.php:521
msgid "Result : "
msgstr "النتيجة : "

#: inc/app/class-setup.php:523
msgid "Clear all cache : "
msgstr "مسح جميع التخزين المؤقت : "

#: inc/app/class-setup.php:525
msgid "Delete woocommerce attribute taxonomies transient : "
msgstr "حذف تصنيفات خصائص ووكومرس المؤقتة : "

#: inc/app/class-setup.php:526
msgid "# End update woocommerce product attribute type"
msgstr "# انتهاء تحديث نوع خاصية منتج ووكومرس"

#: inc/app/class-setup.php:543
msgid "Create missing woocommerce pages"
msgstr "إنشاء صفحات ووكومرس المفقودة"

#: inc/app/class-setup.php:575
msgid "Log remapped term"
msgstr "سجل المصطلح المعاد تعيينه"

#: inc/app/menu/class-menu.php:79
msgid ""
"Please assign a menu to the primary location : Appearance > Menus > Manage "
"Locations"
msgstr ""
"يرجى تعيين قائمة للموقع الأساسي : المظهر > القوائم > إدارة "
"المواقع"

#: inc/app/menu/class-menu.php:133 inc/app/menu/class-menu.php:168
#: inc/app/menu/class-menu.php:204
msgid ""
"Please assign a menu to the account location : Appearance > Menus > Manage "
"Locations"
msgstr ""
"يرجى تعيين قائمة لموقع الحساب : المظهر > القوائم > إدارة "
"المواقع"

#: inc/config/theme-customizer-default.php:428
msgid "Guarantee safe & secure checkout"
msgstr "ضمان دفع آمن ومحمي"

#: inc/config/theme-customizer-default.php:435
msgid "Ask a Question"
msgstr "اطرح سؤالاً"

#: inc/config/theme-menu-location.php:12
msgid "Primary Menu"
msgstr "القائمة الرئيسية"

#: inc/config/theme-menu-location.php:13
msgid "Account Menu"
msgstr "قائمة الحساب"

#: inc/config/theme-menu-location.php:14
msgid "Customer Account Menu"
msgstr "قائمة حساب العميل"

#: inc/config/theme-menu-location.php:15
msgid "Customer Care Menu"
msgstr "قائمة خدمة العملاء"

#: inc/config/theme-plugin-required.php:10
msgid "Elementor"
msgstr "إليمنتور"

#: inc/config/theme-plugin-required.php:16
msgid "Rising Bamboo Core"
msgstr "Rising Bamboo Core"

#: inc/config/theme-plugin-required.php:26
msgid "One Click Demo Import"
msgstr "استيراد العرض التوضيحي بنقرة واحدة"

#: inc/config/theme-plugin-required.php:31
msgid "WooCommerce"
msgstr "ووكومرس"

#: inc/config/theme-plugin-required.php:36
msgid "WPC Smart Wishlist for WooCommerce"
msgstr "قائمة الأمنيات الذكية WPC لووكومرس"

#: inc/config/theme-plugin-required.php:41
msgid "WPC Smart Compare for WooCommerce"
msgstr "المقارنة الذكية WPC لووكومرس"

#: inc/config/theme-plugin-required.php:46
msgid "Contact Form 7"
msgstr "نموذج الاتصال 7"

#: inc/config/theme-setup-wizard-import.php:11
msgid "Essential"
msgstr "أساسي"

#: inc/config/theme-setup-wizard-import.php:18
msgid "Extra Data ( Post, Product, Menu ...)"
msgstr "بيانات إضافية ( مقال، منتج، قائمة ...)"

#: inc/config/theme-setup-wizard.php:27
msgid "Theme Setup"
msgstr "إعداد القالب"

#: inc/config/theme-setup-wizard.php:30
#. translators: 1: Title Tag 2: Theme Name 3: Closing Title Tag
msgid "%1$s%2$s Themes &lsaquo; Theme Setup: %3$s%4$s"
msgstr "%1$s%2$s قوالب &lsaquo; إعداد القالب: %3$s%4$s"

#: inc/config/theme-setup-wizard.php:31
msgid "Return to the dashboard"
msgstr "العودة إلى لوحة التحكم"

#: inc/config/theme-setup-wizard.php:32
msgid "Disable this wizard"
msgstr "تعطيل هذا المعالج"

#: inc/config/theme-setup-wizard.php:34
msgid "Skip"
msgstr "تخطي"

#: inc/config/theme-setup-wizard.php:35 woocommerce/myaccount/orders.php:105
msgid "Next"
msgstr "التالي"

#: inc/config/theme-setup-wizard.php:36
msgid "Start"
msgstr "ابدأ"

#: inc/config/theme-setup-wizard.php:37
msgid "Cancel"
msgstr "إلغاء"

#: inc/config/theme-setup-wizard.php:38 inc/config/theme-setup-wizard.php:39
#: inc/config/theme-setup-wizard.php:40
#: inc/tgm/class-tgm-plugin-activation.php:2867
msgid "Install"
msgstr "تثبيت"

#: inc/config/theme-setup-wizard.php:41
msgid "Import"
msgstr "استيراد"

#: inc/config/theme-setup-wizard.php:42
#: inc/tgm/class-tgm-plugin-activation.php:2876
msgid "Activate"
msgstr "تفعيل"

#: inc/config/theme-setup-wizard.php:43
msgid "Later"
msgstr "لاحقاً"

#: inc/config/theme-setup-wizard.php:46
#. translators: Theme Name
msgid "Activate %s"
msgstr "تفعيل %s"

#: inc/config/theme-setup-wizard.php:48
#. translators: Theme Name
msgid "%s is Activated"
msgstr "%s مفعل"

#: inc/config/theme-setup-wizard.php:50
#. translators: Theme Name
msgid "Enter your license key to enable remote updates and theme support."
msgstr "أدخل مفتاح الترخيص لتمكين التحديثات البعيدة ودعم القالب."

#: inc/config/theme-setup-wizard.php:51
msgid "License key"
msgstr "مفتاح الترخيص"

#: inc/config/theme-setup-wizard.php:52
msgid "The theme is already registered, so you can go to the next step!"
msgstr "القالب مسجل بالفعل، يمكنك الانتقال إلى الخطوة التالية!"

#: inc/config/theme-setup-wizard.php:53
msgid "Your theme is activated! Remote updates and theme support are enabled."
msgstr "تم تفعيل قالبك! التحديثات البعيدة ودعم القالب مفعلان."

#: inc/config/theme-setup-wizard.php:54
msgid "Need help?"
msgstr "تحتاج مساعدة؟"

#: inc/config/theme-setup-wizard.php:57
#. translators: Theme Name
msgid "Welcome to %s"
msgstr "مرحباً بك في %s"

#: inc/config/theme-setup-wizard.php:58
msgid "Hi. Welcome back"
msgstr "مرحباً. أهلاً بعودتك"

#: inc/config/theme-setup-wizard.php:59
msgid ""
"This wizard will set up your theme, install plugins, and import content. It "
"is optional & should take only a few minutes."
msgstr ""
"سيقوم هذا المعالج بإعداد قالبك وتثبيت الإضافات واستيراد المحتوى. إنه "
"اختياري ويجب أن يستغرق بضع دقائق فقط."

#: inc/config/theme-setup-wizard.php:60
msgid ""
"You may have already run this theme setup wizard. If you would like to "
"proceed anyway, click on the \"Start\" button below."
msgstr ""
"ربما تكون قد شغلت معالج إعداد القالب هذا بالفعل. إذا كنت تريد "
"المتابعة على أي حال، انقر على زر \"ابدأ\" أدناه."

#: inc/config/theme-setup-wizard.php:62
msgid "Install Child Theme"
msgstr "تثبيت القالب الفرعي"

#: inc/config/theme-setup-wizard.php:63
msgid "You're good to go!"
msgstr "أنت جاهز للانطلاق!"

#: inc/config/theme-setup-wizard.php:64
msgid "Let's build & activate a child theme so you may easily make theme changes."
msgstr "دعنا ننشئ ونفعل قالباً فرعياً حتى تتمكن من إجراء تغييرات القالب بسهولة."

#: inc/config/theme-setup-wizard.php:65
msgid ""
"Your child theme has already been installed and is now activated, if it "
"wasn't already."
msgstr ""
"تم تثبيت قالبك الفرعي بالفعل وهو مفعل الآن، إذا لم يكن كذلك "
"بالفعل."

#: inc/config/theme-setup-wizard.php:66
msgid "Learn about child themes"
msgstr "تعلم عن القوالب الفرعية"

#: inc/config/theme-setup-wizard.php:67
msgid "Awesome. Your child theme has already been installed and is now activated."
msgstr "رائع. تم تثبيت قالبك الفرعي بالفعل وهو مفعل الآن."

#: inc/config/theme-setup-wizard.php:68
msgid "Awesome. Your child theme has been created and is now activated."
msgstr "رائع. تم إنشاء قالبك الفرعي وهو مفعل الآن."

#: inc/config/theme-setup-wizard.php:70
#: inc/tgm/class-tgm-plugin-activation.php:341
msgid "Install Plugins"
msgstr "تثبيت الإضافات"

#: inc/config/theme-setup-wizard.php:71
msgid "You're up to speed!"
msgstr "أنت محدث!"

#: inc/config/theme-setup-wizard.php:72
msgid "Let's install some essential WordPress plugins to get your site up to speed."
msgstr "دعنا نثبت بعض إضافات ووردبريس الأساسية لتسريع موقعك."

#: inc/config/theme-setup-wizard.php:73
msgid ""
"The required WordPress plugins are all installed and up to date. Press "
"\"Next\" to continue the setup wizard."
msgstr ""
"جميع إضافات ووردبريس المطلوبة مثبتة ومحدثة. اضغط "
"\"التالي\" لمتابعة معالج الإعداد."

#: inc/config/theme-setup-wizard.php:74 inc/config/theme-setup-wizard.php:78
#: inc/customizer/panels/panels.php:54
msgid "Advanced"
msgstr "متقدم"

#: inc/config/theme-setup-wizard.php:76
msgid "Import Content"
msgstr "استيراد المحتوى"

#: inc/config/theme-setup-wizard.php:77
msgid ""
"When creating a website from scratch, you should import \"Extra Data\"; "
"otherwise, import only \"Essential Data\"."
msgstr ""
"عند إنشاء موقع ويب من الصفر، يجب استيراد \"البيانات الإضافية\"؛ "
"وإلا، استورد \"البيانات الأساسية\" فقط."

#: inc/config/theme-setup-wizard.php:80
msgid "All done. Have fun!"
msgstr "انتهى كل شيء. استمتع!"

#: inc/config/theme-setup-wizard.php:83
#. translators: Theme Author
msgid "Your theme has been all set up. Enjoy your new theme by %s."
msgstr "تم إعداد قالبك بالكامل. استمتع بقالبك الجديد من %s."

#: inc/config/theme-setup-wizard.php:84
msgid "Extras"
msgstr "إضافات"

#: inc/config/theme-setup-wizard.php:85
msgid "Explore WordPress"
msgstr "استكشف ووردبريس"

#: inc/config/theme-setup-wizard.php:86
msgid "Get Theme Support"
msgstr "احصل على دعم القالب"

#: inc/config/theme-setup-wizard.php:87
msgid "Start Customizing"
msgstr "ابدأ التخصيص"

#: inc/config/theme-sidebars.php:12
msgid "Top Bar"
msgstr "الشريط العلوي"

#: inc/config/theme-sidebars.php:15 inc/config/theme-sidebars.php:25
#: inc/config/theme-sidebars.php:35 inc/config/theme-sidebars.php:55
msgid "Add widgets here."
msgstr "أضف الودجات هنا."

#: inc/config/theme-sidebars.php:22
msgid "Sidebar Blog Top"
msgstr "الشريط الجانبي أعلى المدونة"

#: inc/config/theme-sidebars.php:32
msgid "Sidebar Blog"
msgstr "الشريط الجانبي للمدونة"

#: inc/config/theme-sidebars.php:42
msgid "Sidebar Shop Filter"
msgstr "مرشح الشريط الجانبي للمتجر"

#: inc/config/theme-sidebars.php:45
msgid "Add shop filter widgets here."
msgstr "أضف ودجات مرشح المتجر هنا."

#: inc/config/theme-sidebars.php:52
msgid "Product Category Bottom"
msgstr "أسفل فئة المنتج"

#: inc/customizer/panels/panels.php:18 inc/customizer/sections/blog.php:93
#: inc/customizer/sections/blog.php:228 inc/customizer/sections/general.php:36
#: inc/customizer/sections/logo.php:32
msgid "General"
msgstr "عام"

#: inc/customizer/panels/panels.php:19
msgid "General theme settings"
msgstr "إعدادات القالب العامة"

#: inc/customizer/panels/panels.php:30 inc/customizer/sections/blog.php:31
#: inc/customizer/sections/blog.php:42 inc/customizer/sections/blog.php:163
#: inc/customizer/sections/blog.php:174 inc/customizer/sections/footer.php:33
#: inc/customizer/sections/footer.php:47
#: inc/customizer/sections/woocommerce.php:162
#: inc/customizer/sections/woocommerce.php:1215
#: inc/customizer/sections/woocommerce.php:1245
msgid "Layout"
msgstr "التخطيط"

#: inc/customizer/panels/panels.php:31
msgid "The layout configuration"
msgstr "تكوين التخطيط"

#: inc/customizer/panels/panels.php:42
msgid "Components"
msgstr "المكونات"

#: inc/customizer/panels/panels.php:43
msgid "Other components"
msgstr "مكونات أخرى"

#: inc/customizer/panels/panels.php:55
msgid "Advanced Configuration"
msgstr "التكوين المتقدم"

#: inc/customizer/panels/panels.php:66 inc/helper/class-tag.php:521
msgid "Blog"
msgstr "المدونة"

#: inc/customizer/panels/panels.php:67
msgid "Blog Configuration"
msgstr "تكوين المدونة"

#: inc/customizer/sections/account.php:18
#: inc/customizer/sections/woocommerce.php:19
#: template-parts/components/mobile-navigation.php:65
#: template-parts/headers/default.php:103
#: template-parts/headers/default.php:134
#: template-parts/headers/header-04.php:113
#: template-parts/headers/header-04.php:144
msgid "Account"
msgstr "الحساب"

#: inc/customizer/sections/account.php:20
msgid "This section contains advanced all configurations for Account."
msgstr "يحتوي هذا القسم على جميع التكوينات المتقدمة للحساب."

#: inc/customizer/sections/account.php:31
msgid "Header Account"
msgstr "حساب الرأس"

#: inc/customizer/sections/account.php:41 inc/customizer/sections/search.php:45
msgid "Popup"
msgstr "نافذة منبثقة"

#: inc/customizer/sections/account.php:42
msgid "Show login/register form as popup."
msgstr "عرض نموذج تسجيل الدخول/التسجيل كنافذة منبثقة."

#: inc/customizer/sections/account.php:53 inc/customizer/sections/rating.php:48
#: inc/customizer/sections/scroll-to-top.php:56
#: inc/customizer/sections/search.php:142
#: inc/customizer/sections/woocommerce.php:867
msgid "Icon"
msgstr "أيقونة"

#: inc/customizer/sections/account.php:54
#: inc/customizer/sections/woocommerce.php:597
#: inc/customizer/sections/woocommerce.php:798
msgid "Choose the wish list icon ?"
msgstr "اختر أيقونة قائمة الأمنيات؟"

#: inc/customizer/sections/account.php:67 inc/customizer/sections/rating.php:62
#: inc/customizer/sections/scroll-to-top.php:88
#: inc/customizer/sections/search.php:156
msgid "Icon Size"
msgstr "حجم الأيقونة"

#: inc/customizer/sections/account.php:68
#: inc/customizer/sections/account.php:101
#: inc/customizer/sections/account.php:176
#: inc/customizer/sections/search.php:157
#: inc/customizer/sections/search.php:191
#: inc/customizer/sections/search.php:271
#: inc/customizer/sections/woocommerce.php:194
#: inc/customizer/sections/woocommerce.php:225
#: inc/customizer/sections/woocommerce.php:338
#: inc/customizer/sections/woocommerce.php:378
#: inc/customizer/sections/woocommerce.php:396
#: inc/customizer/sections/woocommerce.php:444
#: inc/customizer/sections/woocommerce.php:611
#: inc/customizer/sections/woocommerce.php:644
#: inc/customizer/sections/woocommerce.php:812
#: inc/customizer/sections/woocommerce.php:881
msgid "Unit : Pixel"
msgstr "الوحدة : بكسل"

#: inc/customizer/sections/account.php:85
#: inc/customizer/sections/scroll-to-top.php:113
#: inc/customizer/sections/search.php:174
msgid "Icon Color"
msgstr "لون الأيقونة"

#: inc/customizer/sections/account.php:100
#: inc/customizer/sections/search.php:190
msgid "Icon Border"
msgstr "حدود الأيقونة"

#: inc/customizer/sections/account.php:118
#: inc/customizer/sections/search.php:224
msgid "Icon Border Radius"
msgstr "نصف قطر حدود الأيقونة"

#: inc/customizer/sections/account.php:119
#: inc/customizer/sections/account.php:216
#: inc/customizer/sections/search.php:225
#: inc/customizer/sections/search.php:311
#: inc/customizer/sections/woocommerce.php:662
msgid ""
"Control <a target=\"_blank\" "
"href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/border-radius\"> "
"border radius</a>."
msgstr ""
"التحكم في <a target=\"_blank\" "
"href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/border-radius\"> "
"نصف قطر الحدود</a>."

#: inc/customizer/sections/account.php:138
#: inc/customizer/sections/search.php:244
msgid "Icon Border Color"
msgstr "لون حدود الأيقونة"

#: inc/customizer/sections/account.php:160
msgid "Content Background Color"
msgstr "لون خلفية المحتوى"

#: inc/customizer/sections/account.php:175
#: inc/customizer/sections/search.php:270
msgid "Input Border"
msgstr "حدود الإدخال"

#: inc/customizer/sections/account.php:193
#: inc/customizer/sections/search.php:288
msgid "Input Border Color"
msgstr "لون حدود الإدخال"

#: inc/customizer/sections/account.php:215
#: inc/customizer/sections/search.php:310
msgid "Input Border Radius"
msgstr "نصف قطر حدود الإدخال"

#: inc/customizer/sections/account.php:234
msgid "Show Edit Account"
msgstr "عرض تحرير الحساب"

#: inc/customizer/sections/account.php:235
msgid "Show/Hide edit account button."
msgstr "إظهار/إخفاء زر تحرير الحساب."

#: inc/customizer/sections/account.php:248
msgid "Edit Account Icon"
msgstr "أيقونة تحرير الحساب"

#: inc/customizer/sections/account.php:249
#: inc/customizer/sections/account.php:308
#: inc/customizer/sections/scroll-to-top.php:57
#: inc/customizer/sections/woocommerce.php:868
msgid "Choose the icon ?"
msgstr "اختر الأيقونة؟"

#: inc/customizer/sections/account.php:269
msgid "Edit Account Icon Color"
msgstr "لون أيقونة تحرير الحساب"

#: inc/customizer/sections/account.php:290
msgid "Show Logout"
msgstr "عرض تسجيل الخروج"

#: inc/customizer/sections/account.php:291
msgid "Show/Hide logout button."
msgstr "إظهار/إخفاء زر تسجيل الخروج."

#: inc/customizer/sections/account.php:307
#: inc/customizer/sections/woocommerce.php:112
msgid "Logout Icon"
msgstr "أيقونة تسجيل الخروج"

#: inc/customizer/sections/account.php:328
msgid "Logout Icon Color"
msgstr "لون أيقونة تسجيل الخروج"

#: inc/customizer/sections/advanced.php:17
msgid "Mega Menu"
msgstr "القائمة الضخمة"

#: inc/customizer/sections/advanced.php:19
msgid "This section contains advanced configurations."
msgstr "يحتوي هذا القسم على التكوينات المتقدمة."

#: inc/customizer/sections/advanced.php:30
msgid "Normalize Classes"
msgstr "تطبيع الفئات"

#: inc/customizer/sections/advanced.php:31
msgid "Remove unnecessary css classes of menu item."
msgstr "إزالة فئات CSS غير الضرورية من عنصر القائمة."

#: inc/customizer/sections/advanced.php:45
#: inc/customizer/sections/advanced.php:57
msgid "404 Error Page"
msgstr "صفحة خطأ 404"

#: inc/customizer/sections/advanced.php:47
msgid "404 Error Page Configuration."
msgstr "تكوين صفحة خطأ 404."

#: inc/customizer/sections/advanced.php:66
msgid "Image"
msgstr "صورة"

#: inc/customizer/sections/advanced.php:80
#: inc/customizer/sections/promotion.php:132
#: inc/customizer/sections/woocommerce.php:536
#: inc/customizer/sections/woocommerce.php:1314
#: inc/customizer/sections/woocommerce.php:1398
msgid "Title"
msgstr "العنوان"

#: inc/customizer/sections/advanced.php:93
#: inc/customizer/sections/promotion.php:180
msgid "Description"
msgstr "الوصف"

#: inc/customizer/sections/blog.php:19
msgid "Blog Category"
msgstr "فئة المدونة"

#: inc/customizer/sections/blog.php:21
msgid "This section contains blog category options."
msgstr "يحتوي هذا القسم على خيارات فئة المدونة."

#: inc/customizer/sections/blog.php:45 inc/customizer/sections/blog.php:177
#: inc/customizer/sections/woocommerce.php:166
#: inc/customizer/sections/woocommerce.php:1219
msgid "Select a layout..."
msgstr "اختر تخطيطاً..."

#: inc/customizer/sections/blog.php:56
msgid "Columns"
msgstr "الأعمدة"

#: inc/customizer/sections/blog.php:73 inc/customizer/sections/blog.php:188
msgid "Sidebar"
msgstr "الشريط الجانبي"

#: inc/customizer/sections/blog.php:76 inc/customizer/sections/blog.php:191
msgid "Choose sidebar..."
msgstr "اختر الشريط الجانبي..."

#: inc/customizer/sections/blog.php:102
msgid "Show author"
msgstr "عرض المؤلف"

#: inc/customizer/sections/blog.php:114 inc/customizer/sections/blog.php:249
msgid "Show Publish Date"
msgstr "عرض تاريخ النشر"

#: inc/customizer/sections/blog.php:126
msgid "Show excerpt"
msgstr "عرض المقتطف"

#: inc/customizer/sections/blog.php:138
msgid "Show comment count"
msgstr "عرض عدد التعليقات"

#: inc/customizer/sections/blog.php:150
msgid "Blog Detail"
msgstr "تفاصيل المدونة"

#: inc/customizer/sections/blog.php:152
msgid "This section contains blog detail options."
msgstr "يحتوي هذا القسم على خيارات تفاصيل المدونة."

#: inc/customizer/sections/blog.php:206
msgid "Feature Image Position"
msgstr "موضع الصورة المميزة"

#: inc/customizer/sections/blog.php:209
#: inc/customizer/sections/woocommerce.php:1520
msgid "Select a position..."
msgstr "اختر موضعاً..."

#: inc/customizer/sections/blog.php:237
msgid "Show Author"
msgstr "عرض المؤلف"

#: inc/customizer/sections/blog.php:261
msgid "Show Category"
msgstr "عرض الفئة"

#: inc/customizer/sections/blog.php:273
msgid "Show Tag"
msgstr "عرض الوسم"

#: inc/customizer/sections/blog.php:285
msgid "Show Comment"
msgstr "عرض التعليق"

#: inc/customizer/sections/blog.php:297
msgid "Show Comment Form"
msgstr "عرض نموذج التعليق"

#: inc/customizer/sections/blog.php:309
msgid "Show Social Share"
msgstr "عرض المشاركة الاجتماعية"

#: inc/customizer/sections/blog.php:323
#: template-parts/contents/related-posts.php:57
msgid "Related Posts"
msgstr "المقالات ذات الصلة"

#: inc/customizer/sections/blog.php:332
msgid "Show Related Posts"
msgstr "عرض المقالات ذات الصلة"

#: inc/customizer/sections/blog.php:344
msgid "Show Navigation"
msgstr "عرض التنقل"

#: inc/customizer/sections/blog.php:356
msgid "Show Pagination"
msgstr "عرض ترقيم الصفحات"

#: inc/customizer/sections/blog.php:368
msgid "Auto Play"
msgstr "التشغيل التلقائي"

#: inc/customizer/sections/blog.php:380
msgid "Auto Play Speed"
msgstr "سرعة التشغيل التلقائي"

#: inc/customizer/sections/footer.php:16
msgid "Footer"
msgstr "التذييل"

#: inc/customizer/sections/footer.php:17
msgid "Theme footer."
msgstr "تذييل القالب."

#: inc/customizer/sections/footer.php:50
msgid "Select a footer..."
msgstr "اختر تذييلاً..."

#: inc/customizer/sections/general.php:21
msgid "Colors"
msgstr "الألوان"

#: inc/customizer/sections/general.php:22
msgid "General colors."
msgstr "الألوان العامة."

#: inc/customizer/sections/general.php:45
msgid "Heading Color"
msgstr "لون العنوان"

#: inc/customizer/sections/general.php:46
msgid "H1, H2 ... H6"
msgstr "H1, H2 ... H6"

#: inc/customizer/sections/general.php:61
msgid "Body Text"
msgstr "نص الجسم"

#: inc/customizer/sections/general.php:62
msgid "Set the color for the body text."
msgstr "تعيين لون نص الجسم."

#: inc/customizer/sections/general.php:77
#: inc/customizer/sections/header.php:225
msgid "Background color"
msgstr "لون الخلفية"

#: inc/customizer/sections/general.php:78
msgid "Visible if the grid is contained."
msgstr "مرئي إذا كانت الشبكة محتواة."

#: inc/customizer/sections/general.php:93
msgid "Primary color"
msgstr "اللون الأساسي"

#: inc/customizer/sections/general.php:108
msgid "Secondary color"
msgstr "اللون الثانوي"

#: inc/customizer/sections/general.php:127
#: inc/customizer/sections/promotion.php:206
msgid "Link"
msgstr "رابط"

#: inc/customizer/sections/general.php:136
msgid "Choose color"
msgstr "اختر اللون"

#: inc/customizer/sections/general.php:137
msgid "This is a color of the link."
msgstr "هذا لون الرابط."

#: inc/customizer/sections/general.php:142
#: inc/customizer/sections/general.php:172
#: inc/customizer/sections/general.php:189
msgid "Default"
msgstr "افتراضي"

#: inc/customizer/sections/general.php:143
#: inc/customizer/sections/general.php:173
#: inc/customizer/sections/general.php:190
msgid "Hover"
msgstr "تمرير"

#: inc/customizer/sections/general.php:157
msgid "General Button"
msgstr "الزر العام"

#: inc/customizer/sections/general.php:166
#: inc/customizer/sections/mobile-navigation.php:73
msgid "Text Color"
msgstr "لون النص"

#: inc/customizer/sections/general.php:167
msgid "This is a color of text."
msgstr "هذا لون النص."

#: inc/customizer/sections/general.php:183
#: inc/customizer/sections/mobile-navigation.php:51
#: inc/customizer/sections/modal.php:78
#: inc/customizer/sections/scroll-to-top.php:135
#: inc/customizer/sections/title.php:142
msgid "Background Color"
msgstr "لون الخلفية"

#: inc/customizer/sections/general.php:184
msgid "This is a color of background button."
msgstr "هذا لون خلفية الزر."

#: inc/customizer/sections/header.php:16
msgid "Header"
msgstr "الرأس"

#: inc/customizer/sections/header.php:17
msgid "Theme header."
msgstr "رأس القالب."

#: inc/customizer/sections/header.php:35
msgid "Heading"
msgstr "العنوان"

#: inc/customizer/sections/header.php:44
msgid "Template"
msgstr "القالب"

#: inc/customizer/sections/header.php:47
msgid "Select a header..."
msgstr "اختر رأساً..."

#: inc/customizer/sections/header.php:59
msgid "Navigation background color"
msgstr "لون خلفية التنقل"

#: inc/customizer/sections/header.php:60
msgid "Background color of the navigation."
msgstr "لون خلفية التنقل."

#: inc/customizer/sections/header.php:75
msgid "Login Form"
msgstr "نموذج تسجيل الدخول"

#: inc/customizer/sections/header.php:76
msgid "On/Off login form in header"
msgstr "تشغيل/إيقاف نموذج تسجيل الدخول في الرأس"

#: inc/customizer/sections/header.php:88 inc/customizer/sections/search.php:18
#: template-parts/components/search/overlay-form.php:29
#: template-parts/headers/default.php:78
#: template-parts/headers/header-04.php:88
msgid "Search"
msgstr "بحث"

#: inc/customizer/sections/header.php:89
msgid "On/Off search form in header"
msgstr "تشغيل/إيقاف نموذج البحث في الرأس"

#: inc/customizer/sections/header.php:100
msgid "Search Mobile"
msgstr "البحث المحمول"

#: inc/customizer/sections/header.php:101
msgid "On/Off search in menu mobile"
msgstr "تشغيل/إيقاف البحث في القائمة المحمولة"

#: inc/customizer/sections/header.php:112
#: inc/customizer/sections/woocommerce.php:153
msgid "Mini Cart"
msgstr "السلة المصغرة"

#: inc/customizer/sections/header.php:113
msgid "On/Off mini cart feature in header"
msgstr "تشغيل/إيقاف ميزة السلة المصغرة في الرأس"

#: inc/customizer/sections/header.php:125
msgid "Wish List"
msgstr "قائمة الأمنيات"

#: inc/customizer/sections/header.php:126
msgid "On/Off wish list feature in header"
msgstr "تشغيل/إيقاف ميزة قائمة الأمنيات في الرأس"

#: inc/customizer/sections/header.php:144
msgid "Heading Sticky"
msgstr "العنوان الثابت"

#: inc/customizer/sections/header.php:153 inc/customizer/sections/logo.php:41
#: inc/customizer/sections/mobile-navigation.php:39
#: inc/customizer/sections/promotion.php:41
#: inc/customizer/sections/scroll-to-top.php:45
#: inc/customizer/sections/woocommerce.php:496
#: inc/customizer/sections/woocommerce.php:854
#: inc/customizer/sections/woocommerce.php:1274
#: inc/customizer/sections/woocommerce.php:1358
msgid "Enable"
msgstr "تمكين"

#: inc/customizer/sections/header.php:154
msgid "On/Off header sticky feature"
msgstr "تشغيل/إيقاف ميزة الرأس الثابت"

#: inc/customizer/sections/header.php:166
#: inc/customizer/sections/mobile-navigation.php:95
msgid "Behaviour"
msgstr "السلوك"

#: inc/customizer/sections/header.php:167
msgid "Behaviour of header sticky when you scroll down/up the page"
msgstr "سلوك الرأس الثابت عند التمرير لأسفل/أعلى الصفحة"

#: inc/customizer/sections/header.php:173
#: inc/customizer/sections/mobile-navigation.php:102
msgid "Both"
msgstr "كلاهما"

#: inc/customizer/sections/header.php:174
#: inc/customizer/sections/mobile-navigation.php:103
msgid "Sticky on scroll down/up"
msgstr "ثابت عند التمرير لأسفل/أعلى"

#: inc/customizer/sections/header.php:177
#: inc/customizer/sections/mobile-navigation.php:106
msgid "Scroll Up"
msgstr "التمرير لأعلى"

#: inc/customizer/sections/header.php:178
#: inc/customizer/sections/mobile-navigation.php:107
msgid "Sticky on scroll up"
msgstr "ثابت عند التمرير لأعلى"

#: inc/customizer/sections/header.php:181
#: inc/customizer/sections/mobile-navigation.php:110
msgid "Scroll Down"
msgstr "التمرير لأسفل"

#: inc/customizer/sections/header.php:182
#: inc/customizer/sections/mobile-navigation.php:111
msgid "Sticky on scroll down"
msgstr "ثابت عند التمرير لأسفل"

#: inc/customizer/sections/header.php:200
msgid "Height"
msgstr "الارتفاع"

#: inc/customizer/sections/header.php:201
msgid "Height of header sticky."
msgstr "ارتفاع الرأس الثابت."

#: inc/customizer/sections/header.php:226
msgid "Background color of header sticky."
msgstr "لون خلفية الرأس الثابت."

#: inc/customizer/sections/loading.php:17
#: inc/customizer/sections/loading.php:34
msgid "Loading"
msgstr "تحميل"

#: inc/customizer/sections/loading.php:19
msgid "This section contains advanced configurations for \"Loading ...\"."
msgstr "يحتوي هذا القسم على التكوينات المتقدمة لـ \"التحميل ...\"."

#: inc/customizer/sections/loading.php:43
msgid "Block Loading"
msgstr "تحميل الكتلة"

#: inc/customizer/sections/loading.php:46
#: inc/customizer/sections/loading.php:60 inc/customizer/sections/modal.php:127
msgid "Select an effect..."
msgstr "اختر تأثيراً..."

#: inc/customizer/sections/loading.php:57
msgid "Button Loading"
msgstr "تحميل الزر"

#: inc/customizer/sections/logo.php:17
msgid "Logo"
msgstr "الشعار"

#: inc/customizer/sections/logo.php:18
msgid "This section contains general logo options."
msgstr "يحتوي هذا القسم على خيارات الشعار العامة."

#: inc/customizer/sections/logo.php:42
msgid "Show/Hide the logo ?"
msgstr "إظهار/إخفاء الشعار؟"

#: inc/customizer/sections/logo.php:54
msgid "Default Logo"
msgstr "الشعار الافتراضي"

#: inc/customizer/sections/logo.php:55
msgid "Choose default logo."
msgstr "اختر الشعار الافتراضي."

#: inc/customizer/sections/logo.php:60
msgid "Dark Logo"
msgstr "الشعار الداكن"

#: inc/customizer/sections/logo.php:61
msgid "Light Logo"
msgstr "الشعار الفاتح"

#: inc/customizer/sections/logo.php:78 inc/customizer/sections/logo.php:188
msgid "Dark Version"
msgstr "النسخة الداكنة"

#: inc/customizer/sections/logo.php:100 inc/customizer/sections/logo.php:210
msgid "Light Version"
msgstr "النسخة الفاتحة"

#: inc/customizer/sections/logo.php:122 inc/customizer/sections/logo.php:232
msgid "Logo Max Width"
msgstr "العرض الأقصى للشعار"

#: inc/customizer/sections/logo.php:146 inc/customizer/sections/logo.php:256
msgid "Logo Padding"
msgstr "حشو الشعار"

#: inc/customizer/sections/logo.php:147 inc/customizer/sections/logo.php:257
msgid "For e.g: 1em 10rem 1vh 10px"
msgstr "على سبيل المثال: 1em 10rem 1vh 10px"

#: inc/customizer/sections/logo.php:172
msgid "Sticky"
msgstr "ثابت"

#: inc/customizer/sections/logo.php:281
msgid "Mobile"
msgstr "محمول"

#: inc/customizer/sections/logo.php:296
msgid "Logo Mobile Max Width"
msgstr "العرض الأقصى لشعار المحمول"

#: inc/customizer/sections/menu.php:17 inc/customizer/sections/rating.php:80
#: inc/customizer/sections/title.php:56
msgid "Color"
msgstr "اللون"

#: inc/customizer/sections/menu.php:18
msgid "Set colors for menu."
msgstr "تعيين ألوان القائمة."

#: inc/customizer/sections/menu.php:31
msgid "Menu"
msgstr "القائمة"

#: inc/customizer/sections/menu.php:40
msgid "Menu Link"
msgstr "رابط القائمة"

#: inc/customizer/sections/menu.php:41
msgid "Color for menu link."
msgstr "لون رابط القائمة."

#: inc/customizer/sections/menu.php:56
msgid "Menu Link Hover"
msgstr "تمرير رابط القائمة"

#: inc/customizer/sections/menu.php:57
msgid "Color for menu link when hover."
msgstr "لون رابط القائمة عند التمرير."

#: inc/customizer/sections/menu.php:72 inc/customizer/sections/title.php:133
msgid "Background"
msgstr "الخلفية"

#: inc/customizer/sections/mobile-navigation.php:17
#: inc/customizer/sections/mobile-navigation.php:30
msgid "Mobile Navigation"
msgstr "التنقل المحمول"

#: inc/customizer/sections/mobile-navigation.php:19
msgid ""
"This section contains configurations for Mobile Navigation. Please turn on "
"mobile mode when configuring."
msgstr ""
"يحتوي هذا القسم على تكوينات التنقل المحمول. يرجى تشغيل "
"الوضع المحمول عند التكوين."

#: inc/customizer/sections/mobile-navigation.php:96
msgid "Behaviour of mobile navigation when you scroll down/up the page"
msgstr "سلوك التنقل المحمول عند التمرير لأسفل/أعلى الصفحة"

#: inc/customizer/sections/modal.php:17 inc/customizer/sections/modal.php:34
msgid "Modal"
msgstr "النافذة المنبثقة"

#: inc/customizer/sections/modal.php:19
msgid "This section contains advanced configurations for \"Modal\"."
msgstr "يحتوي هذا القسم على التكوينات المتقدمة لـ \"النافذة المنبثقة\"."

#: inc/customizer/sections/modal.php:42
msgid "Background Blur"
msgstr "ضبابية الخلفية"

#: inc/customizer/sections/modal.php:53
msgid "Blur Level"
msgstr "مستوى الضبابية"

#: inc/customizer/sections/modal.php:54
#: inc/customizer/sections/scroll-to-top.php:89
msgid "Unit : pixel"
msgstr "الوحدة : بكسل"

#: inc/customizer/sections/modal.php:100
msgid "Opacity"
msgstr "الشفافية"

#: inc/customizer/sections/modal.php:124
msgid "Effect"
msgstr "التأثير"

#: inc/customizer/sections/modal.php:130
#: inc/customizer/sections/woocommerce.php:542
#: inc/customizer/sections/woocommerce.php:1320
#: inc/customizer/sections/woocommerce.php:1404
msgid "None"
msgstr "لا شيء"

#: inc/customizer/sections/modal.php:131
msgid "Slide In Out Down"
msgstr "انزلاق للداخل والخارج لأسفل"

#: inc/customizer/sections/modal.php:132
msgid "Slide In Out Top"
msgstr "انزلاق للداخل والخارج لأعلى"

#: inc/customizer/sections/modal.php:133
msgid "Slide In Out Left"
msgstr "انزلاق للداخل والخارج لليسار"

#: inc/customizer/sections/modal.php:134
msgid "Slide In Out Right"
msgstr "انزلاق للداخل والخارج لليمين"

#: inc/customizer/sections/modal.php:135
msgid "Zoom In Out"
msgstr "تكبير للداخل والخارج"

#: inc/customizer/sections/modal.php:136
msgid "Rotate In Out Down"
msgstr "دوران للداخل والخارج لأسفل"

#: inc/customizer/sections/modal.php:137
msgid "Mix In Animations"
msgstr "خلط الرسوم المتحركة"

#: inc/customizer/sections/modal.php:147
msgid "Click outside to close"
msgstr "انقر خارجاً للإغلاق"

#: inc/customizer/sections/modal.php:159
msgid "\"ESC\" to close"
msgstr "\"ESC\" للإغلاق"

#: inc/customizer/sections/navigation.php:17
#: inc/customizer/sections/navigation.php:30
msgid "Post Navigation"
msgstr "تنقل المقال"

#: inc/customizer/sections/navigation.php:19
msgid "This section contains advanced configurations for \"Post Navigation\"."
msgstr "يحتوي هذا القسم على التكوينات المتقدمة لـ \"تنقل المقال\"."

#: inc/customizer/sections/navigation.php:39
msgid "Single Post"
msgstr "مقال واحد"

#: inc/customizer/sections/navigation.php:40
msgid "Display navigation for single post (or attachment, or custom post type)"
msgstr "عرض التنقل للمقال الواحد (أو المرفق، أو نوع المقال المخصص)"

#: inc/customizer/sections/navigation.php:52
msgid "Page"
msgstr "صفحة"

#: inc/customizer/sections/navigation.php:53
msgid "Display navigation for page"
msgstr "عرض التنقل للصفحة"

#: inc/customizer/sections/navigation.php:65
msgid "Attachment"
msgstr "مرفق"

#: inc/customizer/sections/navigation.php:66
msgid "Display navigation for attachment"
msgstr "عرض التنقل للمرفق"

#: inc/customizer/sections/navigation.php:78
msgid "Other Post Type"
msgstr "نوع مقال آخر"

#: inc/customizer/sections/navigation.php:79
msgid "Display navigation for other post type"
msgstr "عرض التنقل لنوع المقال الآخر"

#: inc/customizer/sections/promotion.php:19
#: inc/customizer/sections/promotion.php:32
msgid "Promotion Popup"
msgstr "النافذة المنبثقة الترويجية"

#: inc/customizer/sections/promotion.php:21
msgid "This section contains configurations for Promotion Popup."
msgstr "يحتوي هذا القسم على تكوينات النافذة المنبثقة الترويجية."

#: inc/customizer/sections/promotion.php:53
#: inc/tgm/class-tgm-plugin-activation.php:2697
msgid "Type"
msgstr "النوع"

#: inc/customizer/sections/promotion.php:56
msgid "Select type..."
msgstr "اختر النوع..."

#: inc/customizer/sections/promotion.php:59
msgid "Promotion"
msgstr "ترويج"

#: inc/customizer/sections/promotion.php:60
msgid "Newsletter"
msgstr "النشرة الإخبارية"

#: inc/customizer/sections/promotion.php:79
msgid "Form"
msgstr "نموذج"

#: inc/customizer/sections/promotion.php:82
msgid "Select form..."
msgstr "اختر نموذجاً..."

#: inc/customizer/sections/promotion.php:105
msgid "Newsletter Image"
msgstr "صورة النشرة الإخبارية"

#: inc/customizer/sections/promotion.php:156
msgid "Sub Title"
msgstr "العنوان الفرعي"

#: inc/customizer/sections/promotion.php:225
msgid "Promotion Image"
msgstr "صورة الترويج"

#: inc/customizer/sections/promotion.php:252
msgid "Delay to show"
msgstr "تأخير العرض"

#: inc/customizer/sections/promotion.php:253
msgid "Popup will be show after a number of milliseconds."
msgstr "ستظهر النافذة المنبثقة بعد عدد من الميلي ثانية."

#: inc/customizer/sections/promotion.php:272
msgid "Repeat"
msgstr "تكرار"

#: inc/customizer/sections/promotion.php:273
msgid "Popup will be show again after a number of minutes."
msgstr "ستظهر النافذة المنبثقة مرة أخرى بعد عدد من الدقائق."

#: inc/customizer/sections/promotion.php:292
msgid "Don't show again"
msgstr "لا تظهر مرة أخرى"

#: inc/customizer/sections/promotion.php:311
msgid "Don't show again expried"
msgstr "انتهت صلاحية عدم الإظهار مرة أخرى"

#: inc/customizer/sections/promotion.php:312
msgid "Set a number of minutes"
msgstr "تعيين عدد من الدقائق"

#: inc/customizer/sections/woocommerce.php:127
#: inc/customizer/sections/woocommerce.php:141
#: woocommerce/cart/mini-cart-ajax.php:31
msgid "Cart"
msgstr "السلة"

#: inc/customizer/sections/woocommerce.php:128
msgid "Cart & Mini Cart Settings."
msgstr "إعدادات السلة والسلة المصغرة."

#: inc/customizer/sections/woocommerce.php:168
msgid "Dropdown"
msgstr "قائمة منسدلة"

#: inc/customizer/sections/woocommerce.php:169
msgid "Canvas"
msgstr "لوحة"

#: inc/customizer/sections/woocommerce.php:179
msgid "Cart Icon"
msgstr "أيقونة السلة"

#: inc/customizer/sections/woocommerce.php:180
msgid "Choose the mini cart icon ?"
msgstr "اختر أيقونة السلة المصغرة؟"

#: inc/customizer/sections/woocommerce.php:193
msgid "Cart Size"
msgstr "حجم السلة"

#: inc/customizer/sections/woocommerce.php:210
msgid "Cart Icon Color"
msgstr "لون أيقونة السلة"

#: inc/customizer/sections/woocommerce.php:224
msgid "Cart Icon Border"
msgstr "حدود أيقونة السلة"

#: inc/customizer/sections/woocommerce.php:241
msgid "Cart Icon Border Radius"
msgstr "نصف قطر حدود أيقونة السلة"

#: inc/customizer/sections/woocommerce.php:242
msgid ""
"Control <a target=\"_blank\" "
"href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/border-radius\"> "
"border radius </a>."
msgstr ""
"التحكم في <a target=\"_blank\" "
"href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/border-radius\"> "
"نصف قطر الحدود </a>."

#: inc/customizer/sections/woocommerce.php:260
msgid "Cart Icon Border Color"
msgstr "لون حدود أيقونة السلة"

#: inc/customizer/sections/woocommerce.php:281
#: inc/customizer/sections/woocommerce.php:703
msgid "Counting Text Color"
msgstr "لون نص العد"

#: inc/customizer/sections/woocommerce.php:295
#: inc/customizer/sections/woocommerce.php:718
msgid "Counting Background"
msgstr "خلفية العد"

#: inc/customizer/sections/woocommerce.php:309
#: inc/customizer/sections/woocommerce.php:733
msgid "Counting Position"
msgstr "موضع العد"

#: inc/customizer/sections/woocommerce.php:322
msgid "Cart Content Background"
msgstr "خلفية محتوى السلة"

#: inc/customizer/sections/woocommerce.php:337
msgid "Cart Content Border"
msgstr "حدود محتوى السلة"

#: inc/customizer/sections/woocommerce.php:355
msgid "Cart Content Border Color"
msgstr "لون حدود محتوى السلة"

#: inc/customizer/sections/woocommerce.php:377
msgid "Product Image Size"
msgstr "حجم صورة المنتج"

#: inc/customizer/sections/woocommerce.php:395
msgid "Remove Button Size"
msgstr "حجم زر الإزالة"

#: inc/customizer/sections/woocommerce.php:413
msgid "Remove Button Text Color"
msgstr "لون نص زر الإزالة"

#: inc/customizer/sections/woocommerce.php:428
msgid "Remove Button Background"
msgstr "خلفية زر الإزالة"

#: inc/customizer/sections/woocommerce.php:443
msgid "Remove Button Border"
msgstr "حدود زر الإزالة"

#: inc/customizer/sections/woocommerce.php:461
msgid "Remove Button Border Color"
msgstr "لون حدود زر الإزالة"

#: inc/customizer/sections/woocommerce.php:488
msgid "Cross-sells"
msgstr "المبيعات المتقاطعة"

#: inc/customizer/sections/woocommerce.php:507
#: inc/customizer/sections/woocommerce.php:1285
#: inc/customizer/sections/woocommerce.php:1369
msgid "Post per page"
msgstr "مقال لكل صفحة"

#: inc/customizer/sections/woocommerce.php:518
#: inc/customizer/sections/woocommerce.php:1296
#: inc/customizer/sections/woocommerce.php:1380
msgid "Column"
msgstr "عمود"

#: inc/customizer/sections/woocommerce.php:529
#: inc/customizer/sections/woocommerce.php:1307
#: inc/customizer/sections/woocommerce.php:1391
msgid "Order By"
msgstr "ترتيب حسب"

#: inc/customizer/sections/woocommerce.php:533
#: inc/customizer/sections/woocommerce.php:1311
#: inc/customizer/sections/woocommerce.php:1395
msgid "Select condition..."
msgstr "اختر الشرط..."

#: inc/customizer/sections/woocommerce.php:535
#: inc/customizer/sections/woocommerce.php:1313
#: inc/customizer/sections/woocommerce.php:1397
msgid "Random"
msgstr "عشوائي"

#: inc/customizer/sections/woocommerce.php:537
#: inc/customizer/sections/woocommerce.php:1315
#: inc/customizer/sections/woocommerce.php:1399
msgid "ID"
msgstr "المعرف"

#: inc/customizer/sections/woocommerce.php:538
#: inc/customizer/sections/woocommerce.php:1316
#: inc/customizer/sections/woocommerce.php:1400
#: woocommerce/myaccount/my-orders.php:17
msgid "Date"
msgstr "التاريخ"

#: inc/customizer/sections/woocommerce.php:539
#: inc/customizer/sections/woocommerce.php:1317
#: inc/customizer/sections/woocommerce.php:1401
msgid "Modified"
msgstr "معدل"

#: inc/customizer/sections/woocommerce.php:540
#: inc/customizer/sections/woocommerce.php:1318
#: inc/customizer/sections/woocommerce.php:1402
msgid "Menu order"
msgstr "ترتيب القائمة"

#: inc/customizer/sections/woocommerce.php:541
#: inc/customizer/sections/woocommerce.php:1319
#: inc/customizer/sections/woocommerce.php:1403 woocommerce/cart/cart.php:33
#: woocommerce/cart/cart.php:98
msgid "Price"
msgstr "السعر"

#: inc/customizer/sections/woocommerce.php:551
#: inc/customizer/sections/woocommerce.php:1329
#: inc/customizer/sections/woocommerce.php:1413
#: woocommerce/myaccount/my-orders.php:16
msgid "Order"
msgstr "الطلب"

#: inc/customizer/sections/woocommerce.php:555
#: inc/customizer/sections/woocommerce.php:1333
#: inc/customizer/sections/woocommerce.php:1417
msgid "Select order..."
msgstr "اختر الطلب..."

#: inc/customizer/sections/woocommerce.php:557
#: inc/customizer/sections/woocommerce.php:1335
#: inc/customizer/sections/woocommerce.php:1419
msgid "Ascending"
msgstr "تصاعدي"

#: inc/customizer/sections/woocommerce.php:558
#: inc/customizer/sections/woocommerce.php:1336
#: inc/customizer/sections/woocommerce.php:1420
msgid "Descending"
msgstr "تنازلي"

#: inc/customizer/sections/woocommerce.php:573
#: inc/customizer/sections/woocommerce.php:587 inc/helper/class-tag.php:553
#: template-parts/components/mobile-navigation.php:52
#: template-parts/headers/default.php:157
#: template-parts/headers/header-04.php:167
msgid "Wishlist"
msgstr "قائمة الأمنيات"

#: inc/customizer/sections/woocommerce.php:574
msgid "Wishlist Settings."
msgstr "إعدادات قائمة الأمنيات."

#: inc/customizer/sections/woocommerce.php:587
msgid "in header"
msgstr "في الرأس"

#: inc/customizer/sections/woocommerce.php:596
#: inc/customizer/sections/woocommerce.php:756
msgid "Wishlist Icon"
msgstr "أيقونة قائمة الأمنيات"

#: inc/customizer/sections/woocommerce.php:610
msgid "Wish List Icon Size"
msgstr "حجم أيقونة قائمة الأمنيات"

#: inc/customizer/sections/woocommerce.php:628
msgid "Wish List Icon Color"
msgstr "لون أيقونة قائمة الأمنيات"

#: inc/customizer/sections/woocommerce.php:643
msgid "Wishlist Icon Border"
msgstr "حدود أيقونة قائمة الأمنيات"

#: inc/customizer/sections/woocommerce.php:661
msgid "Wishlist Icon Border Radius"
msgstr "نصف قطر حدود أيقونة قائمة الأمنيات"

#: inc/customizer/sections/woocommerce.php:681
msgid "Wishlist Icon Border Color"
msgstr "لون حدود أيقونة قائمة الأمنيات"

#: inc/customizer/sections/woocommerce.php:747
msgid "General Wishlist"
msgstr "قائمة الأمنيات العامة"

#: inc/customizer/sections/woocommerce.php:757
msgid "Choose the wishlist general icon ?"
msgstr "اختر أيقونة قائمة الأمنيات العامة؟"

#: inc/customizer/sections/woocommerce.php:774 inc/helper/class-tag.php:536
msgid "Compare"
msgstr "مقارنة"

#: inc/customizer/sections/woocommerce.php:775
msgid "Compare Settings."
msgstr "إعدادات المقارنة."

#: inc/customizer/sections/woocommerce.php:788
msgid "General Compare"
msgstr "المقارنة العامة"

#: inc/customizer/sections/woocommerce.php:797
msgid "Compare Icon"
msgstr "أيقونة المقارنة"

#: inc/customizer/sections/woocommerce.php:811
msgid "Compare Icon Size"
msgstr "حجم أيقونة المقارنة"

#: inc/customizer/sections/woocommerce.php:831
#: inc/customizer/sections/woocommerce.php:845 inc/helper/class-tag.php:570
msgid "Quick View"
msgstr "عرض سريع"

#: inc/customizer/sections/woocommerce.php:832
msgid "Quick View Settings."
msgstr "إعدادات العرض السريع."

#: inc/customizer/sections/woocommerce.php:855
msgid "Enable/Disable the quick view ?"
msgstr "تمكين/تعطيل العرض السريع؟"

#: inc/customizer/sections/woocommerce.php:880
msgid "Quick View Icon Size"
msgstr "حجم أيقونة العرض السريع"

#: inc/customizer/sections/woocommerce.php:899
msgid "Product Detail"
msgstr "تفاصيل المنتج"

#: inc/customizer/sections/woocommerce.php:900
msgid "Product Detail Settings."
msgstr "إعدادات تفاصيل المنتج."

#: inc/customizer/sections/woocommerce.php:913
msgid "Images"
msgstr "الصور"

#: inc/customizer/sections/woocommerce.php:924
msgid "Image layout"
msgstr "تخطيط الصورة"

#: inc/customizer/sections/woocommerce.php:927
msgid "Select a image layout..."
msgstr "اختر تخطيط صورة..."

#: inc/customizer/sections/woocommerce.php:939
#: inc/customizer/sections/woocommerce.php:966
msgid "Thumbnail Position"
msgstr "موضع الصورة المصغرة"

#: inc/customizer/sections/woocommerce.php:942
#: inc/customizer/sections/woocommerce.php:969
#: inc/customizer/sections/woocommerce.php:1690
msgid "Select position..."
msgstr "اختر الموضع..."

#: inc/customizer/sections/woocommerce.php:991
#: inc/customizer/sections/woocommerce.php:1715
msgid "Thumbnail Images"
msgstr "الصور المصغرة"

#: inc/customizer/sections/woocommerce.php:1025
msgid "Summary"
msgstr "الملخص"

#: inc/customizer/sections/woocommerce.php:1034
msgid "Show Product Excerpt"
msgstr "عرض مقتطف المنتج"

#: inc/customizer/sections/woocommerce.php:1046
msgid "Show Product SKU"
msgstr "عرض رمز المنتج"

#: inc/customizer/sections/woocommerce.php:1058
msgid "Show Product Category"
msgstr "عرض فئة المنتج"

#: inc/customizer/sections/woocommerce.php:1070
msgid "Show Product Tag"
msgstr "عرض وسم المنتج"

#: inc/customizer/sections/woocommerce.php:1082
msgid "Show Sharing"
msgstr "عرض المشاركة"

#: inc/customizer/sections/woocommerce.php:1094
msgid "Show Guarantee"
msgstr "عرض الضمان"

#: inc/customizer/sections/woocommerce.php:1106
msgid "Guarantee Text"
msgstr "نص الضمان"

#: inc/customizer/sections/woocommerce.php:1125
msgid "Guarantee Image"
msgstr "صورة الضمان"

#: inc/customizer/sections/woocommerce.php:1147
#: inc/customizer/sections/woocommerce.php:1180
msgid "Contact Form"
msgstr "نموذج الاتصال"

#: inc/customizer/sections/woocommerce.php:1159
msgid "Contact Form Text"
msgstr "نص نموذج الاتصال"

#: inc/customizer/sections/woocommerce.php:1184
msgid "Select Contact Form..."
msgstr "اختر نموذج الاتصال..."

#: inc/customizer/sections/woocommerce.php:1206
msgid "Data"
msgstr "البيانات"

#: inc/customizer/sections/woocommerce.php:1237
msgid "Related & Upsell Layout"
msgstr "تخطيط المنتجات ذات الصلة والبيع الإضافي"

#: inc/customizer/sections/woocommerce.php:1249
msgid "Select layout..."
msgstr "اختر التخطيط..."

#: inc/customizer/sections/woocommerce.php:1251
msgid "Tabs"
msgstr "علامات التبويب"

#: inc/customizer/sections/woocommerce.php:1252
msgid "List"
msgstr "قائمة"

#: inc/customizer/sections/woocommerce.php:1266
#: inc/woocommerce/class-woocommerce.php:174
#: woocommerce/single-product/related.php:31
msgid "Related Products"
msgstr "المنتجات ذات الصلة"

#: inc/customizer/sections/woocommerce.php:1350
msgid "Upsells"
msgstr "البيع الإضافي"

#: inc/customizer/sections/woocommerce.php:1438
msgid "Woocommerce"
msgstr "ووكومرس"

#: inc/customizer/sections/woocommerce.php:1449
msgid "Categories per row"
msgstr "الفئات لكل صف"

#: inc/customizer/sections/woocommerce.php:1453
msgid "How many categories should be shown per row on shop or category page?"
msgstr "كم عدد الفئات التي يجب عرضها لكل صف في صفحة المتجر أو الفئة؟"

#: inc/customizer/sections/woocommerce.php:1481
msgid "Layout Type"
msgstr "نوع التخطيط"

#: inc/customizer/sections/woocommerce.php:1485
msgid "Select a layout type..."
msgstr "اختر نوع التخطيط..."

#: inc/customizer/sections/woocommerce.php:1487
msgid "Full Width"
msgstr "العرض الكامل"

#: inc/customizer/sections/woocommerce.php:1488
msgid "Container"
msgstr "الحاوية"

#: inc/customizer/sections/woocommerce.php:1498
msgid "Pagination Type"
msgstr "نوع ترقيم الصفحات"

#: inc/customizer/sections/woocommerce.php:1502
msgid "Select a pagination type..."
msgstr "اختر نوع ترقيم الصفحات..."

#: inc/customizer/sections/woocommerce.php:1504
msgid "Pagination"
msgstr "ترقيم الصفحات"

#: inc/customizer/sections/woocommerce.php:1505
msgid "Load More"
msgstr "تحميل المزيد"

#: inc/customizer/sections/woocommerce.php:1506
msgid "Infinity Scroll"
msgstr "التمرير اللانهائي"

#: inc/customizer/sections/woocommerce.php:1516
msgid "Filter Position"
msgstr "موضع المرشح"

#: inc/customizer/sections/woocommerce.php:1522
msgid "Off"
msgstr "إيقاف"

#: inc/customizer/sections/woocommerce.php:1523
msgid "Left"
msgstr "يسار"

#: inc/customizer/sections/woocommerce.php:1524
msgid "Right"
msgstr "يمين"

#: inc/customizer/sections/woocommerce.php:1525
msgid "Top"
msgstr "أعلى"

#: inc/customizer/sections/woocommerce.php:1526
msgid "Left Canvas"
msgstr "لوحة يسار"

#: inc/customizer/sections/woocommerce.php:1527
msgid "Right Canvas"
msgstr "لوحة يمين"

#: inc/customizer/sections/woocommerce.php:1528
msgid "Top Canvas"
msgstr "لوحة أعلى"

#: inc/customizer/sections/woocommerce.php:1529
msgid "Bottom Canvas"
msgstr "لوحة أسفل"

#: inc/customizer/sections/woocommerce.php:1541
msgid "Product Item"
msgstr "عنصر المنتج"

#: inc/customizer/sections/woocommerce.php:1550
msgid "Show Wishlist"
msgstr "عرض قائمة الأمنيات"

#: inc/customizer/sections/woocommerce.php:1562
msgid "Show Compare"
msgstr "عرض المقارنة"

#: inc/customizer/sections/woocommerce.php:1574
msgid "Show Rating"
msgstr "عرض التقييم"

#: inc/customizer/sections/woocommerce.php:1586
msgid "Show Quick View"
msgstr "عرض العرض السريع"

#: inc/customizer/sections/woocommerce.php:1598
msgid "Show Add To Cart"
msgstr "عرض إضافة إلى السلة"

#: woocommerce/cart/cart.php:32
msgid "Product"
msgstr "المنتج"

#: woocommerce/cart/cart.php:34
msgid "Quantity"
msgstr "الكمية"

#: woocommerce/cart/cart.php:35
msgid "Subtotal"
msgstr "المجموع الفرعي"

#: woocommerce/checkout/form-checkout.php:23
msgid "Billing details"
msgstr "تفاصيل الفوترة"

#: woocommerce/checkout/form-checkout.php:27
msgid "Ship to a different address?"
msgstr "الشحن إلى عنوان مختلف؟"

#: woocommerce/checkout/form-checkout.php:31
msgid "Shipping details"
msgstr "تفاصيل الشحن"

#: woocommerce/checkout/form-checkout.php:44
msgid "Additional information"
msgstr "معلومات إضافية"

#: woocommerce/checkout/form-checkout.php:51
msgid "Your order"
msgstr "طلبك"

#: woocommerce/myaccount/dashboard.php:19
msgid "Hello %1$s (not %1$s? %2$s)"
msgstr "مرحباً %1$s (لست %1$s؟ %2$s)"

#: woocommerce/myaccount/dashboard.php:21
msgid ""
"From your account dashboard you can view your %1$srecent orders%2$s, manage "
"your %3$sshipping and billing addresses%4$s, and %5$sedit your password and "
"account details%6$s."
msgstr ""
"من لوحة تحكم حسابك يمكنك عرض %1$sطلباتك الأخيرة%2$s، وإدارة "
"%3$sعناوين الشحن والفوترة%4$s، و%5$sتحرير كلمة المرور وتفاصيل الحساب%6$s."

#: woocommerce/myaccount/form-login.php:26
msgid "Username or email address"
msgstr "اسم المستخدم أو عنوان البريد الإلكتروني"

#: woocommerce/myaccount/form-login.php:30
msgid "Password"
msgstr "كلمة المرور"

#: woocommerce/myaccount/form-login.php:36
msgid "Log in"
msgstr "تسجيل الدخول"

#: woocommerce/myaccount/form-login.php:39
msgid "Remember me"
msgstr "تذكرني"

#: woocommerce/myaccount/form-login.php:45
msgid "Lost your password?"
msgstr "نسيت كلمة المرور؟"

#: woocommerce/myaccount/form-login.php:62
msgid "Register"
msgstr "تسجيل"

#: woocommerce/myaccount/form-login.php:68
msgid "Email address"
msgstr "عنوان البريد الإلكتروني"

#: woocommerce/myaccount/form-login.php:84
msgid ""
"Your personal data will be used to support your experience throughout this "
"website, to manage access to your account, and for other purposes described "
"in our %s."
msgstr ""
"ستُستخدم بياناتك الشخصية لدعم تجربتك في هذا الموقع، وإدارة الوصول إلى "
"حسابك، ولأغراض أخرى موضحة في %s."

#: woocommerce/myaccount/form-login.php:84
msgid "privacy policy"
msgstr "سياسة الخصوصية"

#: woocommerce/myaccount/my-orders.php:18
msgid "Status"
msgstr "الحالة"

#: woocommerce/myaccount/my-orders.php:19
msgid "Total"
msgstr "المجموع"

#: woocommerce/myaccount/my-orders.php:20
msgid "Actions"
msgstr "الإجراءات"

#: woocommerce/myaccount/orders.php:32
msgid "Browse products"
msgstr "تصفح المنتجات"

#: woocommerce/myaccount/orders.php:40
msgid "No orders have been made yet."
msgstr "لم يتم إجراء أي طلبات بعد."

#: woocommerce/single-product/add-to-cart/simple.php:30
msgid "Add to cart"
msgstr "أضف إلى السلة"

#: woocommerce/single-product/meta.php:26
msgid "SKU:"
msgstr "رمز المنتج:"

#: woocommerce/single-product/meta.php:32
msgid "Category:"
msgstr "الفئة:"

#: woocommerce/single-product/meta.php:38
msgid "Tag:"
msgstr "الوسم:"

#: woocommerce/single-product/product-image.php:61
msgid "Click to enlarge"
msgstr "انقر للتكبير"

#: woocommerce/single-product/tabs/description.php:27
msgid "Description"
msgstr "الوصف"

#: woocommerce/single-product/tabs/additional-information.php:25
msgid "Additional information"
msgstr "معلومات إضافية"

#: woocommerce/single-product/tabs/reviews.php:30
msgid "Reviews (%d)"
msgstr "التقييمات (%d)"

#: template-parts/components/breadcrumb.php:35
msgid "You are here:"
msgstr "أنت هنا:"

#: template-parts/components/breadcrumb.php:39
msgid "Home"
msgstr "الرئيسية"

#: template-parts/components/search/overlay-form.php:30
msgid "Search for:"
msgstr "البحث عن:"

#: template-parts/components/search/overlay-form.php:32
msgid "Search..."
msgstr "بحث..."

#: template-parts/components/search/overlay-form.php:35
msgid "Search products"
msgstr "البحث في المنتجات"

#: template-parts/components/search/overlay-form.php:37
msgid "Search products..."
msgstr "البحث في المنتجات..."

#: template-parts/components/search/overlay-form.php:44
msgid "Popular Keywords:"
msgstr "الكلمات المفتاحية الشائعة:"

#: template-parts/contents/content-none.php:17
msgid "Nothing here"
msgstr "لا يوجد شيء هنا"

#: template-parts/contents/content-none.php:23
msgid ""
"It looks like nothing was found at this location. Maybe try one of the links "
"below or a search?"
msgstr ""
"يبدو أنه لم يتم العثور على شيء في هذا الموقع. ربما جرب أحد الروابط "
"أدناه أو البحث؟"

#: template-parts/contents/content-none.php:31
msgid "Most Used Categories"
msgstr "الفئات الأكثر استخداماً"

#: template-parts/contents/content-none.php:48
msgid "Try looking in the monthly archives. 🙂"
msgstr "جرب البحث في الأرشيف الشهري. 🙂"

#: template-parts/contents/content-search.php:17
msgid "Search Results for: %s"
msgstr "نتائج البحث عن: %s"

#: template-parts/contents/content-search.php:23
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "عذراً، لكن لا شيء يطابق مصطلحات البحث الخاصة بك. يرجى المحاولة مرة أخرى بكلمات مفتاحية مختلفة."

#: template-parts/footers/footer-default.php:45
msgid "Proudly powered by %s"
msgstr "مدعوم بفخر من %s"

#: template-parts/headers/default.php:49
msgid "Primary Menu"
msgstr "القائمة الرئيسية"

#: template-parts/headers/default.php:91
msgid "Open search"
msgstr "فتح البحث"

#: template-parts/headers/default.php:116
msgid "View your shopping cart"
msgstr "عرض سلة التسوق الخاصة بك"

#: template-parts/headers/default.php:170
msgid "View your wishlist"
msgstr "عرض قائمة أمنياتك"

#: template-parts/page-title/page-title-default.php:25
msgid "Archives: %s"
msgstr "الأرشيف: %s"

#: template-parts/page-title/page-title-default.php:29
msgid "Author: %s"
msgstr "المؤلف: %s"

#: template-parts/page-title/page-title-default.php:37
msgid "Daily Archives: %s"
msgstr "الأرشيف اليومي: %s"

#: template-parts/page-title/page-title-default.php:39
msgid "Monthly Archives: %s"
msgstr "الأرشيف الشهري: %s"

#: template-parts/page-title/page-title-default.php:41
msgid "Yearly Archives: %s"
msgstr "الأرشيف السنوي: %s"

#: template-parts/page-title/page-title-default.php:45
msgid "Asides"
msgstr "جانبيات"

#: template-parts/page-title/page-title-default.php:49
msgid "Galleries"
msgstr "المعارض"

#: template-parts/page-title/page-title-default.php:53
msgid "Images"
msgstr "الصور"

#: template-parts/page-title/page-title-default.php:57
msgid "Videos"
msgstr "الفيديوهات"

#: template-parts/page-title/page-title-default.php:61
msgid "Quotes"
msgstr "الاقتباسات"

#: template-parts/page-title/page-title-default.php:65
msgid "Links"
msgstr "الروابط"

#: template-parts/page-title/page-title-default.php:69
msgid "Statuses"
msgstr "الحالات"

#: template-parts/page-title/page-title-default.php:73
msgid "Audio"
msgstr "الصوت"

#: template-parts/page-title/page-title-default.php:77
msgid "Chats"
msgstr "المحادثات"
