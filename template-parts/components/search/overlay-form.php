<?php
/**
 * RisingBambooTheme
 *
 * @package RisingBambooTheme.
 */

use RisingBambooTheme\App\App;
use RisingBambooTheme\Helper\Helper;
use RisingBambooTheme\Helper\Setting;
$modal_effect    = Setting::get(RISING_BAMBOO_KIRKI_FIELD_COMPONENT_MODAL_EFFECT);
$outside         = Setting::get(RISING_BAMBOO_KIRKI_FIELD_COMPONENT_MODAL_CLICK_OUTSIDE_CLOSE);
$backdrop_filter = Setting::get(RISING_BAMBOO_KIRKI_FIELD_COMPONENT_MODAL_BACKDROP_FILTER);
$classes         = [];
$classes[]       = ( true === $backdrop_filter ) ? 'backdrop' : 'backdrop-none';
$classes[]       = ( false === $outside ) ? 'outside-modal' : '';
$classes         = [];
$classes[]       = ( true === $backdrop_filter ) ? 'backdrop' : 'backdrop-none';
$classes[]       = ( false === $outside ) ? 'outside-modal' : '';
$class_string    = implode(' ', array_filter($classes));

?>
<div id="rbb-search-content" class="style-2 rbb-modal invisible opacity-0 fixed transition-all duration-500 <?php echo esc_attr($class_string); ?>">
	<div class="rbb-modal-backdrop"></div>
	<div class="rbb-search-top max-w-[600px] z-[1000]  relative bg-[#F5F2F6] rounded-[6px]  w-full">
		<div class="rbb-content-search flex flex-col lg:p-10 p-6">
			<div class="rbb-text-top flex items-center justify-between">
				<div class="text-search text-[#1C1C1C] text-4xl font-medium">
					<?php echo esc_html__('Search', 'onxen'); ?>
				</div>
				<div onclick="RbbThemeSearch.closeSearchForm(event)" class="close-search relative w-12 h-12 rounded-full text-center leading-10 cursor-pointer">
				</div>
			</div>
			<div id="_desktop_search">
				<?php get_search_form([ 'overlay' => true ]); ?>
			<?php
			echo get_template_part('template-parts/components/search/search-result');
			?>
			</div>
	</div>
	</div>
</div>
