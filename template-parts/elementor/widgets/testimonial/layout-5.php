<?php
/**
 * RisingBambooTheme package
 *
 * @package RisingBambooTheme
 */

if ( $testimonials ) {
	?>
	<div id="<?php echo esc_attr($id); ?>" class="rbb-elementor-slider rbb-testimonial overflow-hidden md:pb-16 pb-8 relative <?php echo esc_attr($layout); ?>">
		<div class="block_content_testimonial">
			<?php if ( $title ) { ?>
				<div class="block_heading mb-[42px] text-center">
					<p class="sub_title mb-7"><?php echo esc_html($sub_title); ?></p>
					<h3 class="title"><?php echo esc_html($title); ?></h3>
				</div>
			<?php } ?>
			<div class="block-masory-item md:columns-3  sm:columns-2 columns-1 sm:gap-[15px] gap-0 md:gap-[30px]">
			<?php
			foreach ( $testimonials as $testimonial ) {
				?>
				<div class="testimonial-items md:mb-[30px] mb-[15px]">
					<div class="block_content shadow-[4px_4px_20px_0_rgba(0,0,0,0.1)] overflow-hidden">
						<div class="flex items-center testimonial_avartar justify-center relative">
							<div class="overflow-hidden">
								<?php
								if ( has_post_thumbnail($testimonial) ) {
									echo get_the_post_thumbnail(
										$testimonial,
										'post-thumbnail',
										[
											'class' => 'w-full object-cover',
											'alt'   => get_the_title($testimonial),
										]
									);
								} else {
									?>
									<img class="object-cover" src="<?php echo esc_url(get_stylesheet_directory_uri() . '/dist/images/default-thumbnail.png'); ?>" alt="<?php echo esc_html('Default post thumbnail'); ?>" >
								<?php } ?>
							</div>
						</div>
						<div class="block_content-text text-center bg-white px-4 pt-6 pb-10">
							<div class="testimonial_ratings flex justify-center text-base text-[var(--rbb-general-primary-color)] mb-6 md:mb-8">
								<i class="rbb-icon-rating-start-filled-2 mx-0.5"></i>
								<i class="rbb-icon-rating-start-filled-2 mx-0.5"></i>
								<i class="rbb-icon-rating-start-filled-2 mx-0.5"></i>
								<i class="rbb-icon-rating-start-filled-2 mx-0.5"></i>
								<i class="rbb-icon-rating-start-filled-2 mx-0.5"></i>
							</div>
							<div class="testimonial_text text-sm md:text-base text-[var(--rbb-general-body-text-color)] relative leading-6 mb-6 md:mb-8">
								<?php echo wp_kses_post($testimonial->post_content); ?>
							</div>
							<div class="block_content-name flex items-center justify-center mb-4">
								<div class="testimonial_info md:text-base text-sm leading-7 font-medium text-[#000000]"><?php echo esc_html($testimonial->post_title); ?></div>
							</div>
							<div class="testimonial_excerpt text-sm leading-5 text-[var(--rbb-general-primary-color)]"><i class="rbb-icon-check-4 inline-block mr-2 mb-1"></i><?php echo esc_html($testimonial->post_excerpt); ?></div>
						</div>
					</div>
				</div>
			<?php } ?>
		</div>
	</div>
</div>
<?php } ?>
