/*
 * This is a CSS comment.
<<<<<<< HEAD
 * This is a merge conflict...
=======
 * which should be detected.
>>>>>>> ref/heads/feature-branch
 */

.SettingsTabPaneWidgetType-tab-mid {
   background: transparent url(tab_inact_mid.png) repeat-x;
<<<<<<< HEAD
   line-height: -25px;
=======
   line-height: -20px;
>>>>>>> ref/heads/feature-branch
   cursor: pointer;
   -moz-user-select: none;
}

/*
 * The above tests are based on "normal" tokens.
 * The below test checks that once the tokenizer breaks down because of
 * unexpected merge conflict boundaries, subsequent boundaries will still
 * be detected correctly.
 */

/*
 * This is a CSS comment.
<<<<<<< HEAD
 * This is a merge conflict...
=======
 * which should be detected.
>>>>>>> ref/heads/feature-branch
 */
