<?xml version="1.0"?>
<documentation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://phpcsstandards.github.io/PHPCSDevTools/phpcsdocs.xsd"
    title="Enforce Curly Brace Namespace Syntax"
    >
    <standard>
    <![CDATA[
    Namespace declarations without curly braces are not allowed.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Namespace declaration with braces.">
        <![CDATA[
namespace Vendor\Project\Scoped <em>{</em>
    // Code.
<em>}</em>
        ]]>
        </code>
        <code title="Invalid: Namespace declaration without braces.">
        <![CDATA[
namespace Vendor\Project\Sub<em>;</em>

// Code
        ]]>
        </code>
    </code_comparison>
</documentation>
