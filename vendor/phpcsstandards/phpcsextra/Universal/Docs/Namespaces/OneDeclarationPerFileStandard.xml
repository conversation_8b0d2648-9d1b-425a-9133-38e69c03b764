<?xml version="1.0"?>
<documentation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://phpcsstandards.github.io/PHPCSDevTools/phpcsdocs.xsd"
    title="One Namespace Declaration Per File"
    >
    <standard>
    <![CDATA[
    There should be only one namespace declaration per file.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: One namespace declaration in a file.">
        <![CDATA[
namespace Vendor\Project\Sub;
        ]]>
        </code>
        <code title="Invalid: Multiple namespace declarations in a file.">
        <![CDATA[
namespace Vendor\Project\Sub\A {
}

<em>namespace Vendor\Project\Sub\B {
}</em>
        ]]>
        </code>
    </code_comparison>
</documentation>
