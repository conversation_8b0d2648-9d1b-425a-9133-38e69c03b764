<?xml version="1.0"?>
<documentation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://phpcsstandards.github.io/PHPCSDevTools/phpcsdocs.xsd"
    title="Disallow Use Const"
    >
    <standard>
    <![CDATA[
    Disallow the use of `use const` import statements, with or without alias.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Other type of use import statements.">
        <![CDATA[
use Vendor\Sub\ClassName;
use function Vendor\Sub\functionName;
        ]]>
        </code>
        <code title="Invalid: `use const` import statements.">
        <![CDATA[
use const Vendor\Sub\CONST;
use const Vendor\Sub\BAR as otherConst;
        ]]>
        </code>
    </code_comparison>
</documentation>
