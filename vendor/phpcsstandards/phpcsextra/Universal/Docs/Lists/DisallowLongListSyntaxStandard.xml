<?xml version="1.0"?>
<documentation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://phpcsstandards.github.io/PHPCSDevTools/phpcsdocs.xsd"
    title="Disallow Long List Syntax"
    >
    <standard>
    <![CDATA[
    Short list syntax must be used.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Short form of list.">
        <![CDATA[
<em>[</em>$a, $b<em>]</em> = $array;
        ]]>
        </code>
        <code title="Invalid: Long form of list.">
        <![CDATA[
<em>list(</em>$a, $b<em>)</em> = $array;
        ]]>
        </code>
    </code_comparison>
</documentation>
