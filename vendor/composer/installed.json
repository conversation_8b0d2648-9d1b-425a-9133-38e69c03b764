{"packages": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/composer-installer.git", "reference": "6e0fa428497bf560152ee73ffbb8af5c6a56b0dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/composer-installer/zipball/6e0fa428497bf560152ee73ffbb8af5c6a56b0dd", "reference": "6e0fa428497bf560152ee73ffbb8af5c6a56b0dd", "shasum": ""}, "require": {"composer-plugin-api": "^2.2", "php": ">=5.4", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "^2.2", "ext-json": "*", "ext-zip": "*", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpcompatibility/php-compatibility": "^9.0", "yoast/phpunit-polyfills": "^1.0"}, "time": "2025-06-27T17:24:01+00:00", "type": "composer-plugin", "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://frenck.dev", "role": "Open source developer"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "security": "https://github.com/PHPCSStandards/composer-installer/security/policy", "source": "https://github.com/PHPCSStandards/composer-installer"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "install-path": "../dealerdirect/phpcodesniffer-composer-installer"}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "time": "2019-12-27T09:44:58+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "install-path": "../phpcompatibility/php-compatibility"}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git", "reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "time": "2024-04-24T21:30:46+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}, "funding": [{"url": "https://github.com/PHPCompatibility", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "install-path": "../phpcompatibility/phpcompatibility-paragonie"}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "2.1.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git", "reference": "5bfbbfbabb3df2b9a83e601de9153e4a7111962c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/5bfbbfbabb3df2b9a83e601de9153e4a7111962c", "reference": "5bfbbfbabb3df2b9a83e601de9153e4a7111962c", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0", "squizlabs/php_codesniffer": "^3.3"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "time": "2025-05-12T16:38:37+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityWP/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}, "funding": [{"url": "https://github.com/PHPCompatibility", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcompatibility", "type": "thanks_dev"}], "install-path": "../phpcompatibility/phpcompatibility-wp"}, {"name": "phpcsstandards/phpcsextra", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSExtra.git", "reference": "fa4b8d051e278072928e32d817456a7fdb57b6ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSExtra/zipball/fa4b8d051e278072928e32d817456a7fdb57b6ca", "reference": "fa4b8d051e278072928e32d817456a7fdb57b6ca", "shasum": ""}, "require": {"php": ">=5.4", "phpcsstandards/phpcsutils": "^1.1.0", "squizlabs/php_codesniffer": "^3.13.0 || ^4.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpcsstandards/phpcsdevcs": "^1.1.6", "phpcsstandards/phpcsdevtools": "^1.2.1", "phpunit/phpunit": "^4.5 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "time": "2025-06-14T07:40:39+00:00", "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSExtra/graphs/contributors"}], "description": "A collection of sniffs and standards for use with PHP_CodeSniffer.", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHPCSExtra/issues", "security": "https://github.com/PHPCSStandards/PHPCSExtra/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSExtra"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "install-path": "../phpcsstandards/phpcsextra"}, {"name": "phpcsstandards/phpcsutils", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSUtils.git", "reference": "65355670ac17c34cd235cf9d3ceae1b9252c4dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSUtils/zipball/65355670ac17c34cd235cf9d3ceae1b9252c4dad", "reference": "65355670ac17c34cd235cf9d3ceae1b9252c4dad", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^3.13.0 || ^4.0"}, "require-dev": {"ext-filter": "*", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpcsstandards/phpcsdevcs": "^1.1.6", "yoast/phpunit-polyfills": "^1.1.0 || ^2.0.0 || ^3.0.0"}, "time": "2025-06-12T04:32:33+00:00", "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["PHPCSUtils/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "description": "A suite of utility functions for use with PHP_CodeSniffer", "homepage": "https://phpcsutils.com/", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "phpcs3", "phpcs4", "standards", "static analysis", "tokens", "utility"], "support": {"docs": "https://phpcsutils.com/", "issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "security": "https://github.com/PHPCSStandards/PHPCSUtils/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSUtils"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "install-path": "../phpcsstandards/phpcsutils"}, {"name": "squizlabs/php_codesniffer", "version": "3.13.2", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "5b5e3821314f947dd040c70f7992a64eac89025c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5b5e3821314f947dd040c70f7992a64eac89025c", "reference": "5b5e3821314f947dd040c70f7992a64eac89025c", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "time": "2025-06-17T22:17:01+00:00", "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "install-path": "../squizlabs/php_codesniffer"}, {"name": "woocommerce/qit-cli", "version": "0.10.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/woocommerce/qit-cli.git", "reference": "42c4722bb71940dc0435103775439588e923e1cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/qit-cli/zipball/42c4722bb71940dc0435103775439588e923e1cd", "reference": "42c4722bb71940dc0435103775439588e923e1cd", "shasum": ""}, "require": {"ext-curl": "*", "php": "^7.2.5 | ^8"}, "time": "2025-05-20T15:58:42+00:00", "bin": ["qit"], "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "description": "A command line interface for WooCommerce Quality Insights Toolkit (QIT).", "support": {"issues": "https://github.com/woocommerce/qit-cli/issues", "source": "https://github.com/woocommerce/qit-cli/tree/0.10.0"}, "install-path": "../woocommerce/qit-cli"}, {"name": "woocommerce/woocommerce-sniffs", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/woocommerce/woocommerce-sniffs.git", "reference": "3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/woocommerce-sniffs/zipball/3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8", "reference": "3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0.0", "php": ">=7.0", "phpcompatibility/phpcompatibility-wp": "^2.1.0", "wp-coding-standards/wpcs": "^3.0.0"}, "time": "2023-09-29T13:52:33+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WooCommerce sniffs", "keywords": ["phpcs", "standards", "static analysis", "woocommerce", "wordpress"], "support": {"issues": "https://github.com/woocommerce/woocommerce-sniffs/issues", "source": "https://github.com/woocommerce/woocommerce-sniffs/tree/1.0.0"}, "install-path": "../woocommerce/woocommerce-sniffs"}, {"name": "wp-coding-standards/wpcs", "version": "3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/9333efcbff231f10dfd9c56bb7b65818b4733ca7", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7", "shasum": ""}, "require": {"ext-filter": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-xmlreader": "*", "php": ">=5.4", "phpcsstandards/phpcsextra": "^1.2.1", "phpcsstandards/phpcsutils": "^1.0.10", "squizlabs/php_codesniffer": "^3.9.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "suggest": {"ext-iconv": "For improved results", "ext-mbstring": "For improved results"}, "time": "2024-03-25T16:39:00+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "funding": [{"url": "https://opencollective.com/php_codesniffer", "type": "custom"}], "install-path": "../wp-coding-standards/wpcs"}], "dev": true, "dev-package-names": ["dealerdirect/phpcodesniffer-composer-installer", "phpcompatibility/php-compatibility", "phpcompatibility/phpcompatibility-paragonie", "phpcompatibility/phpcompatibility-wp", "phpcsstandards/phpcsextra", "phpcsstandards/phpcsutils", "squizlabs/php_codesniffer", "woocommerce/qit-cli", "woocommerce/woocommerce-sniffs", "wp-coding-standards/wpcs"]}