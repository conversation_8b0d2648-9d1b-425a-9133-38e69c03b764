<?php return array(
    'root' => array(
        'name' => 'a/onxen',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'a/onxen' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dealerdirect/phpcodesniffer-composer-installer' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'reference' => '6e0fa428497bf560152ee73ffbb8af5c6a56b0dd',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../dealerdirect/phpcodesniffer-composer-installer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpcompatibility/php-compatibility' => array(
            'pretty_version' => '9.3.5',
            'version' => '9.3.5.0',
            'reference' => '9fb324479acf6f39452e0655d2429cc0d3914243',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../phpcompatibility/php-compatibility',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpcompatibility/phpcompatibility-paragonie' => array(
            'pretty_version' => '1.3.3',
            'version' => '1.3.3.0',
            'reference' => '293975b465e0e709b571cbf0c957c6c0a7b9a2ac',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../phpcompatibility/phpcompatibility-paragonie',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpcompatibility/phpcompatibility-wp' => array(
            'pretty_version' => '2.1.7',
            'version' => '2.1.7.0',
            'reference' => '5bfbbfbabb3df2b9a83e601de9153e4a7111962c',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../phpcompatibility/phpcompatibility-wp',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpcsstandards/phpcsextra' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'reference' => 'fa4b8d051e278072928e32d817456a7fdb57b6ca',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../phpcsstandards/phpcsextra',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpcsstandards/phpcsutils' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '65355670ac17c34cd235cf9d3ceae1b9252c4dad',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../phpcsstandards/phpcsutils',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'squizlabs/php_codesniffer' => array(
            'pretty_version' => '3.13.2',
            'version' => '3.13.2.0',
            'reference' => '5b5e3821314f947dd040c70f7992a64eac89025c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../squizlabs/php_codesniffer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'woocommerce/qit-cli' => array(
            'pretty_version' => '0.10.0',
            'version' => '0.10.0.0',
            'reference' => '42c4722bb71940dc0435103775439588e923e1cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../woocommerce/qit-cli',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'woocommerce/woocommerce-sniffs' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => '3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../woocommerce/woocommerce-sniffs',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-coding-standards/wpcs' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => '9333efcbff231f10dfd9c56bb7b65818b4733ca7',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../wp-coding-standards/wpcs',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);
