{"name": "phpcompatibility/php-compatibility", "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "type": "phpcodesniffer-standard", "keywords": ["compatibility", "phpcs", "standards"], "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "role": "lead", "homepage": "https://github.com/wimg"}, {"name": "<PERSON>", "role": "lead", "homepage": "https://github.com/jrfnl"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "scripts": {"post-install-cmd": "\"vendor/bin/phpcs\" --config-set installed_paths ../../..", "post-update-cmd": "\"vendor/bin/phpcs\" --config-set installed_paths ../../.."}}