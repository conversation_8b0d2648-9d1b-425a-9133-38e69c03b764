<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="Example Project" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

	<description>A custom set of rules to check for a WPized WordPress project</description>

	<!--
	#############################################################################
	COMMAND LINE ARGUMENTS
	https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki/Annotated-Ruleset
	#############################################################################
	-->

	<file>.</file>

	<!-- Exclude WP Core folders and files from being checked. -->
	<exclude-pattern>/docroot/wp-admin/*</exclude-pattern>
	<exclude-pattern>/docroot/wp-includes/*</exclude-pattern>
	<exclude-pattern>/docroot/wp-*.php</exclude-pattern>
	<exclude-pattern>/docroot/index.php</exclude-pattern>
	<exclude-pattern>/docroot/xmlrpc.php</exclude-pattern>
	<exclude-pattern>/docroot/wp-content/plugins/*</exclude-pattern>

	<!-- Exclude the Composer Vendor directory. -->
	<exclude-pattern>/vendor/*</exclude-pattern>

	<!-- Exclude the Node Modules directory. -->
	<exclude-pattern>/node_modules/*</exclude-pattern>

	<!-- Exclude minified Javascript files. -->
	<exclude-pattern>*.min.js</exclude-pattern>

	<!-- Strip the filepaths down to the relevant bit. -->
	<arg name="basepath" value="."/>

	<!-- Check up to 8 files simultaneously. -->
	<arg name="parallel" value="8"/>


	<!--
	#############################################################################
	SET UP THE RULESETS
	#############################################################################
	-->

	<!-- Include the WordPress-Extra standard. -->
	<rule ref="WordPress-Extra">
		<!--
		We may want a middle ground though. The best way to do this is add the
		entire ruleset, then rule by rule, remove ones that don't suit a project.
		We can do this by running `phpcs` with the '-s' flag, which allows us to
		see the names of the sniffs reporting errors.
		Once we know the sniff names, we can opt to exclude sniffs which don't
		suit our project like so.

		The below two examples just show how you can exclude rules/error codes.
		They are not intended as advice about which sniffs to exclude.
		-->

		<!--
		<exclude name="WordPress.WhiteSpace.ControlStructureSpacing"/>
		<exclude name="Modernize.FunctionCalls.Dirname.Nested"/>
		-->
	</rule>

	<!-- Let's also check that everything is properly documented. -->
	<rule ref="WordPress-Docs"/>

	<!-- Add in some extra rules from other standards. -->
	<rule ref="Generic.Commenting.Todo"/>

	<!-- Check for PHP cross-version compatibility. -->
	<!--
	To enable this, the PHPCompatibilityWP standard needs
	to be installed.
	See the readme for installation instructions:
	https://github.com/PHPCompatibility/PHPCompatibilityWP
	For more information, also see:
	https://github.com/PHPCompatibility/PHPCompatibility
	-->
	<!--
	<config name="testVersion" value="5.6-"/>
	<rule ref="PHPCompatibilityWP">
		<include-pattern>*\.php</include-pattern>
	</rule>
	-->


	<!--
	#############################################################################
	SNIFF SPECIFIC CONFIGURATION
	#############################################################################
	-->

	<!--
	To get the optimal benefits of using WordPressCS, we should add a couple of
	custom properties.
	Adjust the values of these properties to fit our needs.

	For information on additional custom properties available, check out
	the wiki:
	https://github.com/WordPress/WordPress-Coding-Standards/wiki/Customizable-sniff-properties
	-->
	<config name="minimum_wp_version" value="6.0"/>

	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="my-textdomain"/>
				<element value="library-textdomain"/>
			</property>
		</properties>
	</rule>

	<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<properties>
			<property name="prefixes" type="array">
				<element value="my_prefix"/>
			</property>
		</properties>
	</rule>


	<!--
	#############################################################################
	SELECTIVE EXCLUSIONS
	Exclude specific files for specific sniffs and/or exclude sub-groups in sniffs.
	#############################################################################
	-->

	<!--
	Sometimes, you may want to exclude a certain directory, like your tests,
	for select sniffs.
	The below examples demonstrate how to do this.

	In the example, the `GlobalVariablesOverride` rule is excluded for test files
	as it is sometimes necessary to overwrite WP globals in test situations (just
	don't forget to restore them after the test!).

	Along the same lines, PHPUnit is getting stricter about using PSR-4 file names,
	so excluding test files from the `WordPress.Files.Filename` sniff can be a
	legitimate exclusion.

	For more information on ruleset configuration optiones, check out the PHPCS wiki:
	https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki/Annotated-Ruleset
	-->
	<rule ref="WordPress.WP.GlobalVariablesOverride">
		<exclude-pattern>/path/to/Tests/*Test\.php</exclude-pattern>
	</rule>
	<rule ref="WordPress.Files.FileName">
		<exclude-pattern>/path/to/Tests/*Test\.php</exclude-pattern>
	</rule>

</ruleset>
