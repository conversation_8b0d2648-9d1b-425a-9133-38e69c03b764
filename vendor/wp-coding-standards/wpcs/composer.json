{"name": "wp-coding-standards/wpcs", "type": "phpcodesniffer-standard", "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "static analysis", "WordPress"], "license": "MIT", "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "require": {"php": ">=5.4", "ext-filter": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-xmlreader": "*", "squizlabs/php_codesniffer": "^3.9.0", "phpcsstandards/phpcsutils": "^1.0.10", "phpcsstandards/phpcsextra": "^1.2.1"}, "require-dev": {"phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "phpcsstandards/phpcsdevtools": "^1.2.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "php-parallel-lint/php-console-highlighter": "^1.0.0"}, "suggest": {"ext-iconv": "For improved results", "ext-mbstring": "For improved results"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"lint": ["@php ./vendor/php-parallel-lint/php-parallel-lint/parallel-lint . -e php --show-deprecated --exclude vendor --exclude .git"], "check-cs": ["@php ./vendor/squizlabs/php_codesniffer/bin/phpcs"], "fix-cs": ["@php ./vendor/squizlabs/php_codesniffer/bin/phpcbf"], "run-tests": ["@php ./vendor/phpunit/phpunit/phpunit --filter WordPress ./vendor/squizlabs/php_codesniffer/tests/AllTests.php --no-coverage"], "coverage": ["@php ./vendor/phpunit/phpunit/phpunit --filter WordPress ./vendor/squizlabs/php_codesniffer/tests/AllTests.php"], "check-complete": ["@php ./vendor/phpcsstandards/phpcsdevtools/bin/phpcs-check-feature-completeness -q ./WordPress"], "check-complete-strict": ["@php ./vendor/phpcsstandards/phpcsdevtools/bin/phpcs-check-feature-completeness ./WordPress"], "check-all": ["@lint", "@check-cs", "@run-tests", "@check-complete-strict"]}, "scripts-descriptions": {"lint": "Lint PHP files against parse errors.", "check-cs": "Run the PHPCS script against the entire codebase.", "fix-cs": "Run the PHPCBF script to fix all the autofixable violations on the codebase.", "run-tests": "Run all the unit tests for the WordPress Coding Standards sniffs without code coverage.", "coverage": "Run all the unit tests for the WordPress Coding Standards sniffs with code coverage.", "check-complete": "Check if all the sniffs have tests.", "check-complete-strict": "Check if all the sniffs have unit tests and XML documentation.", "check-all": "Run all checks (lint, phpcs, feature completeness) and tests."}, "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki", "source": "https://github.com/WordPress/WordPress-Coding-Standards"}}