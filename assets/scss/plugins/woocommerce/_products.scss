ul.products {
	margin: 0;
	padding: 0;

	li.product {
		list-style: none;
		position: relative;
		margin-bottom: 2em;

		img {
			display: block;
		}

		.button {
			display: block;
		}
	}
}

.item-product {
	.product_price {
		>.woocommerce-Price-amount {
			bdi {
				color: var(--rbb-general-primary-color);
			}
		}

		>del {
			~.woocommerce-Price-amount {
				float: left;
				padding-right: 15px;
			}
		}

		ins {
			float: left;
			text-decoration-color: snow;
			color: var(--rbb-general-primary-color);
		}

		>del {
			color: #d6d6d6;
			text-decoration-color: #e82525;

			+.woocommerce-Price-amount {
				float: left;
			}
		}
	}

	.thumbnail-container {
		img {
			transition: all 0.7s linear;
		}
	}

	&:hover {
		.thumbnail-container {
			&.hover-images {
				&.fade_in_image {
					img:not(.image-cover) {
						-webkit-opacity: 0;
						-moz-opacity: 0;
						-ms-opacity: 0;
						-o-opacity: 0;
						opacity: 0;
					}

					.image-cover {
						-webkit-opacity: 1;
						-moz-opacity: 1;
						-ms-opacity: 1;
						-o-opacity: 1;
						opacity: 1;
					}
				}

				&.zoom_in_image {
					img:not(.image-cover) {
						transform: scale(0.9);
						-moz-transform: scale(0.9);
						-webkit-transform: scale(0.9);
					}
				}

				&.zoom_out_image {
					img:not(.image-cover) {
						transform: scale(1.05);
						-moz-transform: scale(1.05);
						-webkit-transform: scale(1.05);
					}
				}

				&.blur_image {
					a {
						&:before {
							-webkit-opacity: 1;
							-moz-opacity: 1;
							-ms-opacity: 1;
							-o-opacity: 1;
							opacity: 1;
						}
					}
				}

				&.shiny_slide {
					>a {
						&:before {
							top: 120%;
							right: -75%;
							transition: all 0.35s ease;
						}
					}
				}
			}
		}
	}

	.thumbnail-container {
		&.hover-images {
			&.fade_in_image {
				img:not(.image-cover) {
					-webkit-opacity: 1;
					-moz-opacity: 1;
					-ms-opacity: 1;
					-o-opacity: 1;
					opacity: 1;
				}

				.image-cover {
					-webkit-opacity: 0;
					-moz-opacity: 0;
					-ms-opacity: 0;
					-o-opacity: 0;
					opacity: 0;
				}
			}

			&.zoom_in_image {
				img:not(.image-cover) {
					transform: scale(1);
					-moz-transform: scale(1);
					-webkit-transform: scale(1);
				}
			}

			&.zoom_out_image {
				img:not(.image-cover) {
					transform: scale(0.95);
					-moz-transform: scale(0.95);
					-webkit-transform: scale(0.95);
				}
			}

			&.blur_image {
				a {
					&:before {
						content: "";
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						-webkit-opacity: 0;
						-moz-opacity: 0;
						-ms-opacity: 0;
						-o-opacity: 0;
						opacity: 0;
						transition: 0.3s;
						backdrop-filter: blur(5px);
					}
				}
			}

			&.shiny_slide {
				>a {
					&:before {
						content: "";
						display: inline-block;
						width: 150%;
						height: 80%;
						position: absolute;
						top: -75%;
						right: 32%;
						z-index: 9;
						background: rgba(255, 255, 255, 0.15);
						transform: skewY(-50deg);
						transition: all 0.35s ease;
					}
				}
			}
		}
	}
}
