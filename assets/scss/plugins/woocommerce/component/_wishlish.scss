.rbb-wishlist {
	.wishlist-icon-link {
		border-radius: var(--rbb-wishlist-icon-border-radius);
		border-width: var(--rbb-wishlist-icon-border);
		border-style: solid;
		border-color: var(--rbb-wishlist-icon-border-color);

		&:hover {
			border-color: var(--rbb-general-secondary-color);
			background: var(--rbb-general-secondary-color);

			.wishlist-icon,
			.wishlist-text {
				color: var(--rbb-general-primary-color);
			}
		}

		.wishlist-text {
			font-size: 14px;
			font-weight: 600;
			color: var(--rbb-wishlist-icon-color);
			transition: all 0.35s ease-in;
		}

		.wishlist-icon {
			font-size: var(--rbb-wishlist-icon-size);
			color: var(--rbb-wishlist-icon-color);
			flex-grow: 1;
			transition: all 0.35s ease-in;
			font-weight: 600;
		}
	}

	.wishlist-count {
		width: 20px;
		height: 20px;
		font-size: 0.6875rem;
		line-height: 20px;
		color: var(--rbb-wishlist-count-color);
		background-color: var(--rbb-wishlist-count-background-color);
		position: absolute;
		top: var(--rbb-wishlist-count-position-top);
		bottom: var(--rbb-wishlist-count-position-bottom);
		left: var(--rbb-wishlist-count-position-left);
		right: var(--rbb-wishlist-count-position-right);
		letter-spacing: 0px;
	}
}

#primary {
	.woosw-list {
		margin: 110px 0px 110px 0px;

		table.woosw-items {
			width: 100%;
			border: none;

			tr {
				display: flex;
				align-items: center;
				border: 1px solid var(--rbb-general-button-bg-color);
				margin-bottom: 20px;
				overflow: hidden;
				border-radius: 8px;
				transition: 0.3s;

				&:hover {
					box-shadow: 12px 9px 25px 0px rgba(0, 0, 0, 0.1);
					text-shadow: 12px 9px 25px rgba(0, 0, 0, 0.1);
				}

				td {
					background-color: transparent !important;
					padding: 10px;

					.woosw-item--price {
						color: var(--rbb-general-primary-color);
						font-weight: 800;
						font-size: 12px;

						del {
							color: $woocommerce-price-color;
							-webkit-text-decoration-color: #e82525;
							text-decoration-color: #e82525;
						}

						ins {
							-webkit-text-decoration: none;
							text-decoration: none;
							float: left;
							padding-right: 10px;
						}
					}

					.woosw-item--time {
						padding-top: 10px;
						color: #909090;
						font-size: 10px;
						text-transform: uppercase;
						display: flex;
						align-items: center;

						&:before {
							content: "";
							font-family: rbb-font, serif;
							font-size: 22px;
							padding-right: 8px;
						}
					}

					&.woosw-item--remove {
						padding: 10px 0px 10px 10px;
						width: 35px;

						span {
							background: var(--rbb-general-button-bg-color);
							border-radius: 100%;
							height: 25px;
							width: 25px;
							line-height: 25px;
							text-align: center;
							color: #5e5e5e;
							transition: 0.3s;
							position: relative;

							&:hover {
								color: #fff;
								background: var(--rbb-general-primary-color);

								&:before {
									color: #fff;
								}
							}

							&:before {
								content: "\ead9";
								font-family: rbb-font, serif;
								font-size: 12px;
							}

							&.woosw-item--removing {
								&:before {
									content: "";
									position: absolute;
									top: 50%;
									left: 50%;
									width: 14px;
									height: 14px;
									background: var(--rbb-button-loading);
									background-size: cover;
									transform: translate(-50%, -50%);
									animation: none;
									-webkit-animation: none;
								}

								&:hover {
									&:before {
										background: var(--rbb-button-loading-hover);
										background-size: cover;
									}
								}
							}
						}
					}

					&.woosw-item--image {
						width: 130px;
						height: 140px;
						padding: 15px;

						img {
							width: 100%;
							height: auto;
						}
					}

					.woosw-item--name {
						padding-bottom: 8px;
						font-size: 12px;
					}

					.woosw-item--stock {
						.out-of-stock {
							margin-right: 40px;
							margin-bottom: 0px;
						}

						.in-stock {
							color: #34ad5e;
							margin-right: 40px;

							&:before {
								border: 1px solid #34ad5e;
								color: #34ad5e;
							}

							&:after {
								background: #34ad5e;
							}
						}
					}

					.woosw-item--atc {
						a {
							padding: 16px 20px;
							min-width: 200px;
							text-align: center;
							background: var(--rbb-general-primary-color);
							border-radius: 30px;
							font-weight: bold;
							color: var(--rbb-general-button-hover-color);
							transition: 0.3s;
							display: inline-block;
							position: relative;

							&.added_to_cart {
								display: none;
							}

							&.loading {
								color: transparent;

								&:before {
									content: "";
									position: absolute;
									top: 50%;
									left: 50%;
									width: 18px;
									height: 18px;
									background: var(--rbb-button-loading-hover);
									background-size: cover;
									transform: translate(-50%, -50%);
								}
							}

							&:hover {
								background: var(--rbb-general-secondary-color);
							}
						}
					}

					&.woosw-item--actions {
						align-items: center;
						display: flex;
						margin-right: 0px;
						margin-left: auto;
					}
				}

				&:not(:last-child) {
					border-bottom: 1px solid var(--rbb-general-button-bg-color);
				}
			}
		}

		.woosw-actions {
			max-width: 55%;
			margin-top: 40px;

			.woosw-copy {
				position: relative;
				width: 100%;

				.woosw-copy-label {
					font-weight: bold;
				}

				.woosw-copy-url {
					flex: 1 1 0%;
					margin-left: 25px;
					height: 54px;
					border-bottom-left-radius: 4px;
					border-top-left-radius: 4px;
					background: var(--rbb-general-button-bg-color);
					border: 1px solid var(--rbb-general-button-bg-color);

					#woosw_copy_url {
						width: 100%;
						height: 52px;
						border-radius: 4px;
						box-shadow: none;
						border: 1px solid var(--rbb-general-button-bg-color);

						&:focus {
							border: 1px solid var(--rbb-general-primary-color);
						}
					}
				}

				.woosw-copy-btn {
					background: var(--rbb-general-button-bg-color);
					color: var(--rbb-general-link-color);
					height: 54px;
					padding: 0px 20px;
					line-height: 54px;
					min-width: 90px;
					text-align: center;
					border-bottom-right-radius: 4px;
					border-top-right-radius: 4px;
					cursor: pointer;
					transition: 0.3s;

					&:hover {
						transition: 0.3s;
						color: var(--rbb-general-button-hover-color);
						background: var(--rbb-general-primary-color);
					}
				}
			}
		}
	}
}

@media (min-width: 767px) and (max-width: 991px) {
	#primary .woosw-list .woosw-actions {
		max-width: 70%;
	}
}

@media (max-width: 767px) {
	#primary {
		.woosw-list {
			table.woosw-items tr {
				display: block;

				td {
					&.woosw-item--actions {
						display: block;
						padding-left: 35px;

						.out-of-stock,
						.in-stock {
							margin-bottom: 15px;
						}
					}

					.woosw-item--add {
						margin-bottom: 20px;

						a {
							padding: 10px 20px;
							min-width: 150px;
						}
					}
				}
			}

			.woosw-actions {
				max-width: 100%;
			}
		}
	}
}
