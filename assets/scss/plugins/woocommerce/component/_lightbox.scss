#rbb-gallery-lightbox {
	img:hover {
		cursor: url("../images/cursor/icon-zoom.svg"), auto;
	}
}

.pswp__counter {
	display: none;
}

.pswp__scroll-wrap {
	background-color: #fff;

	.pswp__container {
		transition: transform 0.7s cubic-bezier(0.645, 0.045, 0.355, 1);
		background-color: #fff;
	}
}

.pswp--click-to-zoom.pswp--zoom-allowed {
	.pswp__img {
		cursor: url("../images/cursor/icon-zoom.svg"), auto;
	}
}

.pswp--click-to-zoom.pswp--zoomed-in {
	.pswp__img {
		cursor: grab;
	}
}

.pswp__button {
	&.pswp__button--zoom {
		display: none;
	}

	&.pswp__button--close {
		position: fixed;
		left: 50%;
		transform: translateX(-50%);
		width: 55px;
		height: 55px;
		border-radius: 100%;
		background-color: white;
		margin: 0;
		opacity: 1;
		bottom: 50px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease-in-out !important;
		display: flex;
		align-items: center;
		justify-content: center;

		svg {
			width: 15px;
			height: 15px;
		}

		&:hover,
		&:active,
		&:focus {
			background-color: white;
			transform: translateX(-50%) scale(1.1);

			svg {
				-webkit-animation: topbotom 0.5s ease-in-out forwards;
				animation: topbotom 0.5s ease-in-out forwards;
			}
		}

		.pswp__icn {
			position: relative;
			top: 0px;
			left: 0px;
		}
	}
}

.pswp__button--arrow {
	position: fixed;
	bottom: 55px;
	top: initial;
	width: 45px;
	height: 45px;
	border-radius: 100%;
	background-color: white;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transform: translateX(-50%);
	transition: all 0.2s ease-in-out;

	&.pswp__button--arrow--prev {
		left: calc(50% - 70px);
	}

	&.pswp__button--arrow--next {
		left: calc(50% + 70px);
	}

	.pswp__icn {
		width: 15px;
		height: 15px;
		margin: 0;
		position: static;
		transform: none;
	}

	&:hover,
	&:active,
	&:focus {
		background-color: white;
		transform: translateX(-50%) scale(1.1);
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

		&.pswp__button--arrow--prev {
			svg {
				-webkit-animation: rightleft 0.5s ease-in-out forwards;
				animation: rightleft 0.5s ease-in-out forwards;
			}
		}

		&.pswp__button--arrow--next {
			svg {
				-webkit-animation: leftright 0.5s ease-in-out forwards;
				animation: leftright 0.5s ease-in-out forwards;
			}
		}
	}
}

@-webkit-keyframes topbotom {
	0% {
		transform: translateY(0);
	}

	25% {
		opacity: 0;
		transform: translateY(100%);
	}

	50% {
		opacity: 0;
		transform: translateY(-100%);
	}

	75% {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes topbotom {
	0% {
		transform: translateY(0);
	}

	25% {
		opacity: 0;
		transform: translateY(100%);
	}

	50% {
		opacity: 0;
		transform: translateY(-100%);
	}

	75% {
		opacity: 1;
		transform: translateY(0);
	}
}

@-webkit-keyframes leftright {
	0% {
		transform: translateX(0);
	}

	25% {
		opacity: 0;
		transform: translateX(100%);
	}

	50% {
		opacity: 0;
		transform: translateX(-100%);
	}

	75% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes leftright {
	0% {
		transform: translateX(0);
	}

	25% {
		opacity: 0;
		transform: translateX(100%);
	}

	50% {
		opacity: 0;
		transform: translateX(-100%);
	}

	75% {
		opacity: 1;
		transform: translateX(0);
	}
}

@-webkit-keyframes rightleft {
	0% {
		transform: translateX(0);
	}

	25% {
		opacity: 0;
		transform: translateX(-70%);
	}

	50% {
		opacity: 0;
		transform: translateX(70%);
	}

	75% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes rightleft {
	0% {
		transform: translateX(0);
	}

	25% {
		opacity: 0;
		transform: translateX(-70%);
	}

	50% {
		opacity: 0;
		transform: translateX(70%);
	}

	75% {
		opacity: 1;
		transform: translateX(0);
	}
}
