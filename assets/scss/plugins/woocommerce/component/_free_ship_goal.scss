.rbb-free-ship-goal {
	.rbb-free-ship-goal-text {
	}
	.rbb-free-ship-goal-process-bar {
		.progress-bar {
			transition: width 2s;
			background-color: #eecf52;
			.progress-mark {
				color: #eecf52;
			}
		}
	}

	&.percent-middle {
		.progress-bar {
			background-color: #fe8f00;
			.progress-mark {
				color: #fe8f00;
			}
		}
	}

	&.percent-high {
		.progress-bar {
			transition: width 2s;
			background-color: #ef4c2c;
			.progress-mark {
				color: #ef4c2c;
			}
		}
	}

	&.free-ship-eligible .rbb-free-ship-goal-process-bar {
		.progress-bar {
			transition: width 2s;
			background-color: #1b9730;
			.progress-mark {
				color: #1b9730;
				i {
					position: relative;
					&.icon_check {
						line-height: 20px;
						font-size: 8px;
						background-color: #1b9730;
					}
					&:after {
						z-index: -1;
						position: absolute;
						content: "";
						width: 100%;
						height: 100%;
						background: #108043;
						display: inline-block;
						border-radius: 50%;
						opacity: 0.25;
						top: 0;
						left: 0;
						-webkit-transform: scale(1);
						-moz-transform: scale(1);
						-ms-transform: scale(1);
						-o-transform: scale(1);
						transform: scale(1);
						-webkit-animation-name: scale;
						animation-name: scale;
						-webkit-animation-duration: 1.5s;
						animation-duration: 1.5s;
						-webkit-animation-timing-function: ease-out;
						animation-timing-function: ease-out;
						-webkit-animation-iteration-count: infinite;
						animation-iteration-count: infinite;
					}
				}
			}
		}
	}
}
@-webkit-keyframes scale {
	0% {
		opacity: 0.5;
		transform: scale(0.8);
	}
	100% {
		opacity: 0;
		transform: scale(1.8);
	}
}
@keyframes scale {
	0% {
		opacity: 0.5;
		transform: scale(0.8);
	}
	100% {
		opacity: 0;
		transform: scale(1.8);
	}
}
