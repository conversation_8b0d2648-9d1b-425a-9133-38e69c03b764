// ....................  comments ............
#reviews {
	#comments {
		.commentlist {
			li {
				margin-bottom: 30px;
				border-bottom: 1px solid var(--rbb-breadcrumb-background-color);

				.comment_container {
					margin-bottom: 30px;
					.comment-text {
						padding-left: 30px;
						width: calc(100% - 80px);
						display: inline-block;
						.meta {
							margin-top: 10px;

							.woocommerce-review__author {
								text-transform: capitalize;
							}

							.woocommerce-review__published-date {
								text-transform: uppercase;
								font-size: 10px;
								color: #909090;
							}
						}
					}
				}
			}
		}
	}
}

#review_form_wrapper {
	#review_form {
		.comment-reply-title {
			font-size: 16px;
			color: var(--rbb-general-heading-color);
			font-weight: 800;
			text-transform: uppercase;
			padding-bottom: 20px;
			display: block;
		}
		.comment-form-rating {
			margin-top: 5px;

			.stars {
				margin-top: 5px;

				a {
					color: #c3c3c3;
					font-size: 16px;
				}
			}
		}

		label {
			display: block;
			margin: 10px 0px;
			font-weight: 600;
		}

		textarea,
		input:not(#wp-comment-cookies-consent) {
			width: 100%;
			height: 50px;
			border-radius: 4px;
			border: 1px solid $woocommerce__border-input-color-02;

			&:focus,
			&:hover {
				border: 1px solid var(--rbb-general-primary-color);
				box-shadow: none;
			}
		}

		textarea {
			height: 100px;
		}

		.comment-form-cookies-consent {
			display: flex;
			padding-top: 20px;

			input {
				width: 20px;
				height: 20px;
				border-radius: 3px;
				margin-right: 10px;
				box-shadow: none;
				cursor: pointer;
				border: 1px solid $woocommerce__border-input-color-02;

				&[type="checkbox"] {
					color: var(--rbb-general-primary-color);
				}
			}

			label {
				margin: 0px;
				font-weight: 400;
			}
		}

		.form-submit {
			.submit {
				height: 50px;
				background: var(--rbb-general-primary-color);
				border: none !important;
				padding: 12.5px 30px;
				border-radius: 5px;
				margin-top: 30px;
				color: #fff;
				font-size: 11px;
				cursor: pointer;
				transition: 0.35s;

				&:hover {
					background: var(--rbb-general-secondary-color);
				}
			}
		}
	}
}
