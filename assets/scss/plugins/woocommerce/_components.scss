@import "component/mini_cart";
@import "component/wishlish";
@import "component/star_rating";
@import "component/tabs";
@import "component/password";
@import "component/form";
@import "component/notices";
@import "component/price";
@import "component/free_ship_goal";
@import "component/acccordion";
@import "component/comment";
@import "component/lightbox";
@import "component/swatches";

@media screen and (min-width: 48em) {
	/**
	 * Header cart
	 */
	.site-header-cart {
		.widget_shopping_cart {
			position: absolute;
			top: 100%;
			width: 100%;
			z-index: 999999;
			left: -999em;
			display: block;
			box-shadow: 0 3px 3px rgba(0, 0, 0, 0.2);
		}

		&:hover,
		&.focus {
			.widget_shopping_cart {
				left: 0;
				display: block;
			}
		}
	}
}
