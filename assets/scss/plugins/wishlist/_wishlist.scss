.notiny-theme-woosw {
	border: none;
	box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.1);
	border-radius: 10px;

	.notiny-text {
		font-size: var(--typography-body-font-size);
	}
}

.woosw-popup {
	.woosw-popup-inner {
		.woosw-popup-content {
			min-width: 640px;
			height: auto !important;
			max-height: 700px;
			overflow: initial;
			background: #fff;
			border-radius: 9px;

			.woosw-popup-content-top {
				padding: 11px 60px 11px 40px;
				background-color: #fff;
				border-top-right-radius: 9px;
				border-top-left-radius: 9px;
				height: 70px;

				.woosw-name {
					color: var(--rbb-general-heading-color);
					font-size: 18px;
					text-transform: uppercase;
				}

				.woosw-count-wrapper {
					float: right;
					display: flex;
					color: #909090;
					font-size: 10px;
					transition: 0.35s;

					.woosw-empty {
						color: #909090;
						text-transform: uppercase;
						font-weight: bold;
						text-decoration: none;
						display: block !important;
					}

					&:hover {
						color: var(--rbb-general-primary-color);

						.woosw-empty {
							color: var(--rbb-general-primary-color);
						}
					}

					&::after,
					&::before {
						display: none;
					}
				}

				.woosw-popup-close {
					top: -40px;
					right: -40px;
					width: 48px;
					height: 48px;
					color: #fff;
					border-radius: 50%;
					transition: 0.3s;
					font-size: 16px;

					&:hover {
						transition: 0.3s;
						background: rgba(255, 255, 255, 0.15);
					}
				}
			}

			.woosw-popup-content-mid {
				margin: 0 37px 0 28px;
				padding-right: 23px;
				max-height: 496px;
				flex: initial;
				overflow-y: auto !important;

				&::-webkit-scrollbar {
					width: 0;
					display: none;
				}

				&::before {
					display: none;
				}

				.ps-scrollbar-y-rail,
				.ps-scrollbar-x-rail {
					opacity: 1 !important;
					background-color: #ededed;
					width: 8px;
					border-radius: 8px;

					.ps-scrollbar-y {
						right: 2px;
						width: 4px !important;
						border-radius: 3px !important;
						background: #9c9c9c;
					}
				}

				.woosw-items {
					padding: 0 0;
					margin: 0 0 0 12px;
					overflow-y: initial;

					.woosw-item {
						border: 1px solid #e9e9e9;
						border-radius: 8px;
						padding: 0 20px 0 0;

						&:not(:last-child) {
							margin-bottom: 5px;
						}

						.woosw-item-inner {
							padding: 9px 0;
							position: relative;
							border-top: none;

							.woosw-item--remove {
								position: absolute;
								top: 50%;
								left: 0;
								transform: translate(-50%, -50%);
								z-index: 9;
								background: var(--rbb-general-button-bg-color);
								border-radius: 100%;
								height: 25px;
								width: 25px;
								line-height: 25px;
								text-align: center;
								color: #5e5e5e;
								transition: 0.3s;

								&:hover {
									background: var(--rbb-general-primary-color);

									span {
										&::before {
											color: #fff;
										}

										&.woosw-item--removing {
											&::before {
												background: var(--rbb-button-loading-hover);
												background-size: cover;
											}
										}
									}
								}

								span {
									&::before {
										content: "\ead9";
										font-family: rbb-font, serif;
										font-size: 12px;
									}

									&.woosw-item--removing {
										&::before {
											content: "";
											position: absolute;
											top: 50%;
											left: 50%;
											width: 15px;
											height: 15px;
											background: var(--rbb-button-loading);
											background-size: cover;
											transform: translate(-50%, -50%);
											animation: none;
											-webkit-animation: none;
										}

									}
								}
							}

							.woosw-item--image {
								width: 100px;
								flex: 0 0 100px;
								margin-left: 10px;
							}

							.woosw-item--info {
								max-width: 180px;

								.woosw-item--name {
									font-size: 12px;
									font-family: var(--typography-heading);
								}

								.woosw-item--price {
									font-weight: 800;
									padding-top: 7px;
									font-size: 12px;
									color: var(--rbb-general-primary-color);

									ins {
										float: left;
										text-decoration-color: #fffafaff;
									}

									del {
										color: #d6d6d6;
										margin-left: 15px;
										text-decoration-color: #e82525;
									}
								}

								.woosw-item--time {
									font-size: 10px;
									color: #909090;
									text-transform: uppercase;
									position: relative;
									display: flex;
									align-items: center;

									&::before {
										content: "\eb05";
										font-family: rbb-font, serif;
										font-size: 22px;
										padding-right: 8px;
									}
								}
							}

							.woosw-item--actions {
								display: flex;
								align-items: center;

								.woosw-item--stock {
									max-width: 160px;

									p {
										font-size: 11px;
										align-items: center;
									}
								}

								.woosw-item--atc {
									padding-left: 40px;

									.button {
										font-size: 0;
										width: 45px;
										height: 45px;
										line-height: 45px;
										display: block;
										text-align: center;
										color: var(--rbb-general-button-color);
										background: var(--rbb-general-button-bg-color);
										border-radius: 100%;
										transition: 0.3s;
										font-weight: 400;

										&:hover {
											color: var(--rbb-general-button-hover-color);
											background: var(--rbb-general-button-bg-hover-color);

											&.loading {
												&::before {
													background: var(--rbb-button-loading-hover);
													background-size: cover;
												}
											}
										}

										&::before {
											content: "";
											font-family: rbb-font, serif;
											font-size: 18px;
										}

										&.loading {
											animation-name: spin;
											animation-duration: 1s;
											animation-timing-function: linear;
											animation-iteration-count: infinite;

											&::before {
												content: "";
												position: absolute;
												top: 50%;
												left: 50%;
												width: 20px;
												height: 20px;
												background: var(--rbb-button-loading);
												background-size: cover;
												transform: translate(-50%, -50%);
											}
										}
									}

									.added_to_cart {
										display: none;
									}
								}
							}
						}
					}
				}
			}

			.woosw-popup-content-bot {
				padding: 30px 60px 40px 40px;
				border-bottom-right-radius: 9px;
				border-bottom-left-radius: 9px;

				a {
					width: calc(50% - 5px);
					padding: 17px 20px;
					height: 46px;
					border-bottom: none !important;
					text-align: center;
					border-radius: 4px;
					font-family: var(--typography-button);
					font-size: var(--typography-button-font-size);
					font-weight: var(--typography-button-variant);
					text-transform: var(--typography-button-text-transform);
					letter-spacing: var(--typography-button-letter-spacing);
					background: #efefef;
					transition: 0.3s;
					display: flex;
					align-items: center;
					justify-content: center;

					&:hover {
						color: #fff !important;
						background: var(--rbb-general-primary-color);
					}
				}

				.woosw-notice {
					width: auto;
					left: 40px;
					right: 60px;
					bottom: 40px;
					height: 46px;
					line-height: 46px;
					border-radius: 4px;
					padding: 0 15px;
					color: var(--rbb-general-primary-color);
					font-family: var(--typography-button);
					font-size: var(--typography-button-font-size);
					font-variant: var(--typography-button-variant);
					letter-spacing: var(--typography-button-letter-spacing);
					border: 1px solid #a2e0af;
					background: #eef6f0;
					text-align: left;
					display: flex;
					align-items: center;
					text-transform: capitalize;

					strong {
						padding-right: 5px;
					}

					&::before {
						content: "";
						font-family: rbb-font, serif;
						font-size: 14px;
						padding-right: 8px;
					}
				}
			}
		}
	}
}

@media (max-width: 767px) {
	.woosw-popup .woosw-popup-inner .woosw-popup-content {
		min-width: auto;

		.woosw-popup-content-mid {
			margin: 0 20px 0 20px;

			.woosw-items .woosw-item .woosw-item-inner .woosw-item--actions .woosw-item--stock {
				display: none;
			}
		}

		.woosw-popup-content-top {
			padding: 11px 40px 11px 33px;
		}

		.woosw-popup-content-bot {
			padding: 30px 40px 40px 30px;

			.woosw-notice {
				left: 30px;
				right: 40px;
			}
		}
	}
}

@media (max-width: 430px) {
	.woosw-popup .woosw-popup-inner .woosw-popup-content {
		min-width: auto;
		width: 95%;

		.woosw-popup-content-top {
			padding: 11px 35px 11px 28px;
		}

		.woosw-popup-content-mid {
			margin: 0 15px;

			.woosw-items .woosw-item .woosw-item-inner .woosw-item--image {
				width: 70px;
				flex: 0 0 70px;
			}
		}

		.woosw-popup-content-bot a {
			padding: 17px 10px;
		}

		.woosw-popup-content-bot {
			padding: 30px 35px 40px 25px;

			.woosw-notice {
				left: 25px;
				right: 35px;
			}
		}
	}
}
