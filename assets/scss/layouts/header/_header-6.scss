/* Default Head<PERSON> 3 */
.rbb-default-header {
	&.header-6 {
		padding: 0;

		.header-inner {
			background-color: transparent;
			border-bottom: 1px solid rgba(11, 11, 11, 0.1);
		}

		.rbb-main-navigation {
			.menu-container {
				.level-1 {
					>.a-level {
						&::after {
							content: "";
							position: absolute;
							bottom: 0;
							left: 50%;
							transform: translateX(-50%);
							width: 0%;
							height: 2px;
							background-color: var(--rbb-menu-link-hover-color);
							transition: all 0.35s linear;

						}
					}

					&:hover,
					&.active-parent {
						>.a-level {
							&::after {
								width: 50%;
							}
						}
					}
				}
			}
		}

		@media (max-width: 767px) {
			.header-mobile #_mobile_cart .mini-cart-icon .cart-count {
				background-color: #000;
			}

			.header-mobile .menu-mobile .toggle-megamenu::after,
			.header-mobile .menu-mobile .toggle-megamenu::before,
			.header-mobile .menu-mobile .toggle-megamenu .icon-directional::before {
				background-color: #fff !important;
			}

			.search-mobile {
				color: #fff;
			}

			.header-mobile #_mobile_cart .mini-cart-icon .cart-icon {
				color: #fff !important;
			}
		}
	}
}
