/* Default Head<PERSON> 2 */
.rbb-default-header {

	&.header-2,
	&.header-3,
	&.header-6,
	&.header-7 {
		.rbb-product-search-content2 {
			.rbb-search-form {
				.search-categories {
					order: 0;

					.rbb-icon {
						left: auto;
					}
				}

				.input-group {
					border-radius: var(--rbb-search-input-border-radius);

					.search-icon {
						font-size: var(--rbb-search-icon-size);
					}

					.input-search {
						padding: 0 50px 0 24px;
						border-radius: var(--rbb-search-input-border-radius);
					}
				}
			}
		}

		.rbb-product-search {
			.rbb-product-search-icon-wrap {
				&:hover {
					background-color: transparent;
				}
			}
		}

		.rbb-account {
			.rbb-account-icon-wrap {
				&:hover {
					background-color: transparent;
				}
			}
		}

		.rbb-wishlist {
			.wishlist-icon-link {
				&:hover {
					background-color: transparent;
				}
			}
		}

		.mini-cart-icon {
			&:hover {
				background-color: transparent !important;
				border-color: transparent !important;
			}
		}

		.rbb-header-right {
			>div {
				&:not(:last-child) {
					margin-right: 28px;
				}
			}
		}
	}
}
