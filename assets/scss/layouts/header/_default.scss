/* Default Header 1 */
.rbb-default-header {
	.rbb-header-sticky {
		&.header-stuck {
			-webkit-animation-duration: 1s;
			animation-duration: 1s;
			background-color: var(--rbb-menu-sticky-background-color);
			-webkit-animation-name: slidelidown;
			animation-name: slidelidown;
			box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.05);

			>div {
				padding-top: 0;
				height: 100%;
				background-color: var(--rbb-menu-sticky-background-color);

				.header-inner {
					padding: 0;
					background-color: var(--rbb-menu-sticky-background-color);
				}
			}
		}
	}

	&.header-1,
	&.header-4 {
		.rbb-product-search-content2 {
			.rbb-search-form {
				.search-categories {
					order: 0;

					.rbb-icon {
						left: auto;
					}
				}

				.input-group {
					border-radius: var(--rbb-search-input-border-radius);

					.search-icon {
						font-size: var(--rbb-search-icon-size);
					}

					.input-search {
						padding: 0 50px 0 24px;
						border-radius: var(--rbb-search-input-border-radius);
					}
				}
			}
		}

		.rbb-product-search {
			.rbb-product-search-icon-wrap {
				.rbb-product-search-text {
					font-size: 14px;
				}

				&:hover {
					background-color: transparent;
				}
			}
		}

		.rbb-account {
			.rbb-account-icon-wrap {
				&:hover {
					background-color: transparent;
				}
			}
		}

		.rbb-wishlist {
			.wishlist-icon-link {
				&:hover {
					background-color: transparent;
				}
			}
		}

		.rbb-mini-cart {
			.dropdown {
				.mini-cart-icon {
					&:hover {
						background-color: transparent !important;
						border-color: transparent !important;
					}

					.cart-text {
						font-size: 14px;
					}

					.button-icon {
						padding-right: 20px;

						@media (max-width: 767px) {
							padding-right: 0;
						}
					}
				}
			}
		}


		.rbb-header-right {
			>div {
				&:not(:last-child) {
					margin-right: 28px;
				}
			}
		}
	}

}

@media (max-width: 1024px) {
	.rbb-default-header {
		.toggle-megamenu {
			height: 14px;
			width: 18px;

			&::after,
			&::before {
				content: "";
				width: 100%;
				height: 2px;
				position: absolute;
				left: 0;
				top: 0;
				border-radius: 12px;
				transition: all 0.3s ease;
				background-color: var(--rbb-menu-link-color);
			}

			&::after {
				top: auto;
				bottom: 0;
			}

			.icon-directional {
				&::before {
					content: "";
					width: 100%;
					height: 2px;
					position: absolute;
					left: 0;
					top: 6px;
					border-radius: 12px;
					transition: all 0.3s ease;
					background-color: var(--rbb-menu-link-color);
				}
			}

			&.active {
				&::before {
					transform: rotate3d(0, 0, 1, 45deg);
					top: 5px;
				}

				&::after {
					transform: rotate3d(0, 0, 1, -45deg);
					bottom: 7px;
				}

				.icon-directional {
					&::before {
						background: transparent !important;
					}

				}
			}

			&:hover {

				&::after,
				&::before {
					background-color: var(--rbb-general-secondary-color);
				}

				.icon-directional {
					&::before {
						background-color: var(--rbb-general-secondary-color);
					}
				}
			}
		}
	}
}

@media (max-width: 767px) {
	.rbb-default-header {
		.header-mobile {
			.menu-mobile {
				.toggle-megamenu {

					&::after,
					&::before {
						background-color: #000;
					}

					.icon-directional {
						&::before {
							background-color: #000;
						}
					}
				}
			}

			#_mobile_logo {
				overflow: hidden;

				img {
					max-width: var(--rbb-logo-max-width-mobile);
				}
			}

			#_mobile_cart {
				.mini-cart-icon {
					border: none;
					box-shadow: none;
					background: transparent;
					width: auto;

					button {
						width: auto;
						min-width: auto;
					}

					.cart-icon {
						color: #000;
					}

					.cart-count {
						top: 5px;
						left: 10px;
						transform: translateX(0);
					}
				}
			}
		}

		&.header-1 {
			.header-mobile {
				.rbb-product-search {
					opacity: 1;
					visibility: inherit;
					position: static;

					.rbb-product-search-content2 {
						opacity: 0;
						visibility: hidden;
						position: absolute;
						left: 0;
						right: 0;
						z-index: 9;
					}

					&.active {
						.rbb-product-search-content2 {
							opacity: 1;
							visibility: inherit;
						}
					}
				}
			}
		}
	}
}

@keyframes slidelidown {
	0% {
		opacity: 0;
		-webkit-transform: translateY(-2000px);
		-ms-transform: translateY(-2000px);
		transform: translateY(-2000px);
	}

	100% {
		-webkit-transform: translateY(0);
		-ms-transform: translateY(0);
		transform: translateY(0);
	}
}
