form.wpcf7-form {
	.footer-form {
		position: relative;

		p {
			margin-bottom: 0;
		}

		.wpcf7-form-control-wrap {
			input {
				border-radius: 5px;
				background: #fff;
				margin-bottom: 0;
				height: 64px;
				padding-left: 24px;
				padding-right: 157px;
				border: none;
				box-shadow: 4px 4px 20px 0 rgba(0, 0, 0, 0.1);

				&::placeholder {
					text-transform: capitalize;
					color: #5b5b5f;
					font-weight: 400;
					font-size: 12px;
					line-height: 18px;
				}
			}
		}

		.button,
		.wpcf7-spinner {
			z-index: 9;
			height: 52px;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: 6px;
			width: auto;
			border-radius: 5px;
			min-width: 147px;
			font-size: 14px;
			font-weight: 700;
			color: #fff;
			line-height: 20px;
			text-align: center;
			background-color: var(--rbb-general-primary-color);
			text-transform: capitalize;

			@media (max-width: 425px) {
				min-width: 120px;
			}
		}

		.wpcf7-spinner {
			width: 147px;
			min-width: 147px;
			left: auto;

			@media (max-width: 425px) {
				min-width: 120px;
				width: 120px;
			}
		}

		.wpcf7-not-valid-tip {
			position: absolute !important;
			top: 46px !important;
		}
	}

	&.submitting {
		.button {
			&:hover {
				background-color: var(--rbb-general-secondary-color);
			}
		}

		.wpcf7-spinner {
			z-index: 11;
		}
	}

	.wpcf7-response-output {
		position: absolute;
	}
}

// ............ footer-1 ............
body {
	.elementor-shortcode {
		.rbb_contact_shortcode_phone {
			i {
				font-size: 1.125rem;
				color: var(--rbb-general-primary-color);
			}

			p {
				padding-left: 20px;
				margin-bottom: 0;
				font-weight: 800;
			}
		}

		.rbb_contact_shortcode_address,
		.rbb_contact_shortcode_email {
			i {
				font-size: 1.125rem;
				color: var(--rbb-general-primary-color);
			}

			p {
				padding-left: 20px;
				margin-bottom: 0;
			}
		}
	}

	// ............ footer-4, footer-5 ............
	.rbb-footer-icon-close-white {

		.footer-title1,
		.footer-title2,
		.footer-title3,
		.footer-title4 {
			.elementor-heading-title {
				&::before {
					background: #fff;
					top: 12px;
				}

				&::after {
					background: #fff;
					top: 6px;
				}
			}
		}
	}
}

.copyright {
	a {
		color: #fff;
		transition: 0.3s;

		&:hover {
			color: var(--rbb-general-link-hover-color) !important;
		}
	}
}

.footer-title1,
.footer-title2,
.footer-title3,
.footer-title4 {
	.elementor-icon-list-item {
		a {
			width: auto !important;

			&:hover {
				span {
					color: var(--rbb-general-link-hover-color) !important;
				}
			}
		}
	}
}

@media (max-width: 767px) {
	.newsletter-top {
		&.style2 {
			&::before {
				opacity: 1;
			}
		}

		form.wpcf7-form {
			.footer-form {
				.button {
					padding: 0 25px;
				}
			}
		}
	}

	.footer-title1,
	.footer-title2,
	.footer-title3,
	.footer-title4 {
		.elementor-heading-title {
			position: relative;

			&::before {
				content: "";
				width: 14px;
				height: 1px;
				background: #000;
				float: right;
				font-size: 25px;
				position: relative;
				top: 7px;
			}

			&::after {
				content: "";
				width: 1px;
				height: 14px;
				background: #000;
				position: absolute;
				top: 1px;
				right: 7px;
				transition: 0.3s;
			}

			&.active {
				&::after {
					display: none;
				}
			}
		}

		.footer-content,
		.elementor-icon-list-items {
			display: none;
		}
	}
}

.footer-6 {

	form.wpcf7-form .footer-form .button,
	form.wpcf7-form .footer-form .wpcf7-spinner {
		border-radius: 0;
	}

	form.wpcf7-form .footer-form .wpcf7-form-control-wrap input {
		border-radius: 0;
	}
}

.rbb-footer-8 {
	.wpcf7-not-valid-tip {
		color: #fff;
	}
}
