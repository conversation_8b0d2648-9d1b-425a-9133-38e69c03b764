/* Sidebar Blog Top
--------------------------------------------- */
body {
	&.single-post {
		.rbb-page-title {
			.heading {
				display: none;
			}
		}
	}

	.sidebar-top-content {
		margin-bottom: 25px;

		&.widget_search {
			.wp-block-search__button-outside {
				.wp-block-search__label {
					color: var(--rbb-general-heading-color);
					font-family: var(--typography-heading);
					font-weight: var(--typography-heading-variant);
					letter-spacing: var(--typography-heading-letter-spacing);
					line-height: var(--typography-heading-line-height);
					margin-bottom: 20px;
				}
			}
		}
	}

	.sidebar-blog-top-content {
		&:has(.wp-block-group) {
			background: transparent;
			border: 2px solid #f2f2f2;
			border-radius: 19px;
			margin-bottom: 30px;
			overflow: hidden;
		}

		.wp-block-group {
			.wp-block-group-is-layout-flow {
				.wp-block-heading {
					font-size: 18px;
					margin: 0 30px;
					padding: 13px 0;
					color: var(--rbb-general-heading-color);
					border-bottom: 2px solid #f2f2f2;
				}

				ol,
				ul,
				div.wp-block-archives-list {
					padding: 40px 30px;
				}
			}

			form {
				background: #f2f2f2;

				label {
					font-family: var(--typography-heading);
					font-weight: var(--typography-heading-variant);
				}

				.wp-block-search__inside-wrapper {
					margin-top: 20px;
				}
			}
		}
	}
}

.sidebar-blog-top-content {
	background: var(--rbb-general-primary-color);
	border-radius: 21px;
	margin-bottom: 30px;

	.wp-widget-group__inner-blocks {
		padding: 30px;
		border-radius: 19px;
		background: #f2f2f2;
	}

	.widget-title {
		color: #fff;
		font-size: 18px;
		min-height: 60px;
		padding: 16px 30px;
		display: flex;
		align-items: center;
		justify-items: flex-start;
	}
}

/* Sidebar Blog
--------------------------------------------- */
.sidebar-blog-content {
	border: 2px solid #f2f2f2;
	border-radius: 19px;
	margin-bottom: 30px;
	overflow: hidden;

	.wp-block-columns {
		margin-bottom: 0;
	}

	.widget-title {
		font-size: 18px;
		margin: 0 30px;
		padding: 13px 0;
		color: var(--rbb-general-heading-color);
		border-bottom: 2px solid #f2f2f2;
	}

	.wp-widget-group__inner-blocks {
		padding: 40px 30px;
	}
}

@media (min-width: 767px) and (max-width: 991px) {
	.sidebar-blog-top-content {
		margin-bottom: 30px;

		.wp-widget-group__inner-blocks {
			padding: 20px;
		}

		.widget-title {
			padding: 16px 20px;
		}
	}

	.wp-block-latest-posts li {
		&:not(:last-child) {
			margin-bottom: 20px;
		}

		.wp-block-latest-posts__featured-image {
			max-width: 80px;

			img {
				width: 100%;
				height: auto;
			}
		}
	}

	.sidebar-blog-content {
		.widget-title {
			margin: 0 20px;
		}

		.wp-widget-group__inner-blocks {
			padding: 30px 20px;
		}
	}
}
