.rbb-product-search {
	.rbb-product-search-icon-wrap {
		border-radius: var(--rbb-search-icon-border-radius);
		border-width: var(--rbb-search-icon-border);
		border-style: solid;
		border-color: var(--rbb-search-icon-border-color);
		transition: all 0.3s ease-in;

		&:hover {
			border-color: var(--rbb-general-secondary-color);
			background: var(--rbb-general-secondary-color);

			.rbb-product-search-icon,
			.rbb-product-search-text {
				color: var(--rbb-general-primary-color);
			}
		}

		.rbb-product-search-text {
			font-size: 14px;
			font-weight: 600;
			color: var(--rbb-search-icon-color);
			transition: all 0.35s ease-in;
		}

		.rbb-product-search-icon {
			font-size: var(--rbb-search-icon-size);
			color: var(--rbb-search-icon-color);
			transition: all 0.35s ease-in;
			font-weight: 500;
		}
	}

	&:hover {
		.rbb-product-search-content {
			span {
				color: var(--rbb-general-secondary-color);
			}
		}
	}
}

#rbb-search-content {
	z-index: 999999;
	padding: 0;
	padding-top: 30px;
	padding-bottom: 30px;
	padding-right: 30px;

	&.show {
		.rbb-search-top {
			right: 30px;
		}

		.rbb-search-categories {
			max-width: 100%;
			transition: 0.3s;
			opacity: 1;

			.rbb-icon {
				top: 50%;
				transform: translateY(-50%);
			}
		}

		.rbb-search-form {
			.input-search {
				padding: 0 20px;
			}
		}
	}

	.rbb-search-top {
		position: absolute;
		right: -600px;
		top: 30px;
		height: calc(100% - 60px);
		transition: all 0.35s linear;
		border-radius: 20px;
		z-index: 1000;
		overflow: hidden;

		.rbb-text-top {
			margin-bottom: 40px;
		}

		.close-search {
			position: relative;
			box-shadow: 0 0 10px #0000001a;
			cursor: pointer;
			transition: all 0.35s linear;
			font-size: 0;
			background-color: #fff;

			&::before,
			&::after {
				content: "";
				position: absolute;
				top: 50%;
				left: calc(50% - 8px);
				width: 16px;
				height: 1px;
				background-color: #000;
				transform-origin: center;
				transition: transform 0.3s linear;
			}

			&::before {
				transform: rotate(45deg);
			}

			&::after {
				transform: rotate(-45deg);
			}

			&:hover {

				&::before,
				&::after {
					transform: rotate(0deg);
				}
			}
		}
	}

	.rbb-search-form {
		.rbb-search-categories {
			height: 60px;
			margin-right: 12px;
			position: relative;

			&::after {
				content: "";
				width: 1px;
				height: 15px;
				background-color: #787878;
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				right: -12px;
			}

			span {
				font-size: 12px;
				font-weight: 500;
				color: var(--rbb-general-body-text-color);
				text-transform: capitalize;
			}
		}

		.input-group {
			border: 1px solid #eaeaea;
			border-radius: var(--rbb-search-input-border-radius);

			.current-category {
				color: var(--rbb-general-primary-color);
			}

			.input-search {
				padding-right: 64px;
				padding-left: 12px;
			}
		}

		.input-search {
			height: 60px;
			padding: 0 0;
			border: none;
			border-radius: var(--rbb-search-input-border-radius);

			&:focus {
				border: none;
				box-shadow: none;
			}

			&:focus-visible {
				outline: none;
			}

			&::placeholder {
				color: #787878;
				font-size: 12px;
				line-height: 60px;
				text-transform: capitalize;
			}
		}

		#search {
			height: 48px;
			width: 48px;
			top: 50%;
			transform: translateY(-50%);
			right: 6px;
			background-color: #1c1c1c;
			border-radius: var(--rbb-search-input-border-radius);

			&:hover {
				background-color: var(--rbb-general-primary-color);
			}

			.search-icon {
				color: #fff;
			}
		}

	}

	.rbb_results {
		position: relative;
		opacity: 1;
		visibility: visible;
		top: initial !important;
		left: 0;
		right: 0;
		bottom: initial;
		z-index: 0;

		.rbb-search-result-wrap {
			background-color: transparent;
			padding: 0 !important;
			border: none;
			box-shadow: none;
			border-radius: 0;
			margin-top: 40px;

			.rbb-text-search {
				font-size: 20px;
				color: #000;
				line-height: 1;
				text-transform: capitalize;
				font-weight: 500;
			}

			.rbb-search-popular {
				.button-search-popular {
					border-radius: var(--rbb-search-input-border-radius);
				}
			}
		}

		.rbb-search-result {
			padding-bottom: 0;
			padding-top: 40px;
		}

		.rbb-search-result-ajax {

			height: 475px;
			overflow-y: auto;

			.result {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;


				.search-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					width: 100%;
					background-color: #fff;
					padding: 0;
					border-radius: 5px;

					.img-product-search {
						max-width: 124px;
						margin-right: 20px;
						padding: 8px;
						border-width: 0;
					}

					.item-price {
						max-width: 108px;
						margin-right: 30px;
						margin-left: 15px;
						display: flex;
						flex-direction: column-reverse;

						.woocommerce-Price-amount {
							font-size: 14px;
							font-weight: 600;
							color: #1c1c1c;
						}

						ins {
							.woocommerce-Price-amount {
								font-size: 14px;
								font-weight: 600;
								color: #1c1c1c;

							}

							text-decoration: none;
							margin-bottom: 5px;

						}

						del {
							text-decoration-color: #f00;
							line-height: 1;
							display: flex;

							.woocommerce-Price-amount {
								font-size: 14px;
								font-weight: 600;
								color: #b5b5b5;
							}

						}
					}

					.item-content {
						flex: 1;
					}

				}
			}

			&::-webkit-scrollbar {
				width: 12px;
				height: 12px;
			}

			&::-webkit-scrollbar-track {
				background: #f0f0f0;
				border-radius: 10px;
			}

			&::-webkit-scrollbar-thumb {
				background-color: #888;
				border-radius: 10px;
				border: 3px solid #f0f0f0;
			}

			&::-webkit-scrollbar-thumb:hover {
				background-color: #555;
			}

			&::-webkit-scrollbar-thumb:active {
				background-color: #333;
			}
		}
	}

}

// ...........search categories default ..........
.rbb-search-categories {
	.current-category {
		.rbb-icon {
			position: absolute;
			top: 40%;
			right: 0;
			z-index: 100;
			width: 12px;
			height: 12px;

			&::before {
				content: "";
				left: 0;
				-webkit-transform: rotate(45deg);
				transform: rotate(45deg);
				top: 0.4rem;
				position: absolute;
				width: 0.45rem;
				height: 0.05rem;
				background-color: #b7b7b7;
				display: inline-block;
				-webkit-transition: all 0.2s ease;
				transition: all 0.2s ease;
			}

			&::after {
				content: "";
				right: 0;
				-webkit-transform: rotate(-45deg);
				transform: rotate(-45deg);
				top: 0.4rem;
				position: absolute;
				width: 0.45rem;
				height: 0.05rem;
				background-color: #b7b7b7;
				display: inline-block;
				-webkit-transition: all 0.2s ease;
				transition: all 0.2s ease;
			}
		}

		&:has(+ ul.active) {
			.rbb-icon {
				&::after {
					-webkit-transform: rotate(45deg);
					transform: rotate(45deg);
				}

				&::before {
					-webkit-transform: rotate(-45deg);
					transform: rotate(-45deg);
				}
			}
		}
	}

	ul.categories {
		display: none;
		border: 1px solid rgba(0, 0, 0, 0.15);
		font-size: 1.3rem;
		max-height: 500px;
		overflow-y: scroll;
		min-width: 180px;

		&::-webkit-scrollbar {
			width: 7px;
		}

		&::-webkit-scrollbar-track {
			background: #eaeaea;
			border-radius: 4px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: #222;
			border-radius: 4px;
		}

		&.active {
			display: block;
		}

		li {
			line-height: 16px;
			padding: 6px 20px;

			&:hover,
			&.active {
				color: var(--rbb-general-primary-color);
			}
		}
	}

}

// .............   search default ..................
.rbb-product-search-content {
	span {
		transition: all 0.35s;
	}

	&.active > span {
		opacity: 0;
		transition: all 1s linear;
	}

	#rbb-search-content {
		.close-search {
			visibility: hidden;
			display: none;
		}

		.rbb-search-form {
			.search-categories {
				min-width: 0;
				max-width: 0;
			}

			.input-group {
				.input-search {
					&::placeholder {
						color: #a0a0a0;
						line-height: 48px;
						display: none;
						font-size: 0 !important;
					}

					&:focus {
						padding-top: 13px !important;

						& ~ label {
							top: 13px;
							font-size: 10px;
						}
					}

					&:-webkit-autofill,
					&:not(:placeholder-shown) {
						padding-top: 13px !important;

						& ~ label {
							top: 13px;
							font-size: 10px;
						}
					}
				}
			}
		}

		&.show {
			.close-search {
				visibility: inherit;
				display: block;
			}

			.rbb-search-form {
				.search-categories {
					min-width: 135px;
					max-width: 100%;
					transition: 0.5s linear;
				}
			}

			.rbb-search-top {
				width: 600px;
				visibility: inherit;
				opacity: 1;
				transition: 0.5s linear;

				@media (max-width: 1200px) {
					width: 500px;
				}
			}
		}
	}
}

.rbb_results {
	&.active {
		opacity: 1;
		visibility: inherit;
	}

	.rbb-search-result-ajax {
		.item-content {
			.item-price {
				color: var(--rbb-general-primary-color);
			}
		}

		.search-item {
			>div {
				border-radius: 12px;

				.item-price {
					ins {
						float: left;
						text-decoration-color: #fffafa;
						color: var(--rbb-general-primary-color);
					}

					del {
						color: #d6d6d6;
						margin-left: 10px;
						text-decoration-color: #e82525;
					}
				}
			}
		}
	}

	.button-close-search {
		.close-search {
			position: relative;
			box-shadow: 0 0 10px #0000001a;
			cursor: pointer;
			transition: all 0.35s linear;
			font-size: 0;
			background-color: #fff;

			&::before,
			&::after {
				content: "";
				position: absolute;
				top: 50%;
				left: calc(50% - 8px);
				width: 16px;
				height: 1px;
				background-color: #000;
				transform-origin: center;
				transition: transform 0.3s linear;
			}

			&::before {
				transform: rotate(45deg);
			}

			&::after {
				transform: rotate(-45deg);
			}

			&:hover {

				&::before,
				&::after {
					transform: rotate(0deg);
				}
			}
		}
	}

	&.style-results-2 {
		background: #fff;
		box-shadow: 5px 7px 10px rgba(0, 0, 0, 0.1);

		.rbb-search-result-wrap {
			box-shadow: none;
		}
	}
}

// ..........................Search home2......................
.rbb-product-search-content2 {
	.rbb-search-categories .current-category .rbb-icon {
		left: 0;
	}

	.rbb-search-form {
		.search-categories {
			order: 2;
			min-width: 135px;
		}

		button {
			right: 0;
			top: 0;
			height: 52px;
		}

		.input-group {
			border-radius: var(--rbb-search-input-border-radius);
			border-top-right-radius: 0;
			border-bottom-right-radius: 0;
			box-shadow: 4px 4px 10px 0 rgba(#222, 0.05);

			.input-search {
				padding: 0 20px 0 50px;
				height: 50px;
				max-width: 205px;
				border: none;
				box-shadow: none;
				outline: none;
				border-bottom-left-radius: var(--rbb-search-input-border-radius);
				border-top-left-radius: var(--rbb-search-input-border-radius);

				&::placeholder {
					color: #a0a0a0;
					line-height: 48px;
					display: none;
					font-size: 0;
				}

				&:focus {
					padding-top: 13px !important;

					& ~ label {
						top: 13px;
						font-size: 10px;
					}
				}

				&:-webkit-autofill,
				&:not(:placeholder-shown) {
					padding-top: 13px !important;

					& ~ label {
						top: 13px;
						font-size: 10px;
					}
				}
			}

			label {
				top: 51%;
				left: 24px;
			}

			.btn-search {
				width: 50px;
				transition: 0.3s;
				color: var(--rbb-search-icon-color);

				&:hover {
					color: var(--rbb-general-secondary-color);
				}

				.text-search {
					font-size: 0;
				}
			}
		}
	}
}

// ............ page Search ..................
body {
	&.search {
		&.post-type-archive-product {
			#rbb-page-title {
				background: #fff;

				.heading {
					display: none;
				}

				.rbb-breadcrumb {
					font-weight: 800;
					font-size: 1.875rem;
					line-height: 32px;
					text-transform: uppercase;

					span,
					a {
						display: none;
					}
				}
			}

			#primary {
				padding-top: 150px;
			}
		}

		#rbb-primary {
			.woocommerce-products-header {
				display: none;
			}

			.woocommerce-ordering {
				.rbb-accordion__content {
					min-width: 260px;
					padding: 15px;
					border-radius: 0 10px 10px;
					border: 1px solid var(--rbb-breadcrumb-background-color);
					margin: 0;
					-ms-box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.1);
					-o-box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.1);
					box-shadow: 10px 10px 10px #0000001a;

					.active {
						span {
							border: 4px solid #fff;
							background-color: var(--rbb-general-primary-color);
							-ms-box-shadow: 0 0 0 1px var(--rbb-general-primary-color);
							-o-box-shadow: 0 0 0 1px var(--rbb-general-primary-color);
							box-shadow: 0 0 0 1px var(--rbb-general-primary-color);
						}
					}
				}
			}
		}
	}
}

@media (min-width: 1024px) {
	body {
		&.search {
			#rbb-primary {

				.rbb-product-catalog,
				.rbb-sidebar-shop-filter {
					padding-top: 0;
				}
			}
		}
	}
}

@media (max-width: 1023px) {
	body {
		&.search {
			#rbb-primary {
				.sidebar-filter {
					margin-top: 0;
				}
			}
		}
	}
}

@media (max-width: 767px) {
	.search-mobile {
		&.active {
			i {
				&::before {
					content: "✕";
					font-weight: bold;
					font-size: 18px;
					color: #000;
					position: relative;
					top: -3px;
				}
			}
		}
	}

	.product-search-mobile {
		.input-group {
			border-top: 1px solid #c7d4e0;
			border-bottom: 1px solid #c7d4e0;
			border-radius: 0;

			.icon-search,
			.rbb-search-categories,
			.search-categories {
				display: none;
			}

			.input-search {
				height: 58px;
				max-width: 100%;
				text-align: center;
				padding: 0 20px;
				border: none;
				box-shadow: none;

				&::placeholder {
					font-size: 0.625rem !important;
					font-weight: 600;
					text-transform: uppercase;
				}
			}

			#search {
				display: none;
			}
		}

		&.active {
			opacity: 1;
			visibility: inherit;
		}
	}

	body {
		.canvas-overlay {
			top: 60px;
		}

		.rbb_results {
			top: 120px !important;
			margin-top: 15px;
			opacity: 0;
			visibility: hidden;
			transition: 0.3s;

			&.active {
				opacity: 1;
				visibility: inherit;
				margin-top: 0;
				height: calc(100% - 100px);
			}

			.rbb-search-result-wrap {
				height: 100%;

				.rbb-search-result {
					overflow: hidden;
					height: calc(100% - 100px);

					.rbb-search-result-ajax {
						overflow-y: auto;

					}

					.result {
						max-height: 100%;
						padding-bottom: 50px;
					}
				}
			}
		}
	}
}

@-webkit-keyframes rbb_dropdown {
	0% {
		-webkit-transform-origin: 50% 0;
		transform-origin: 50% 0;
		-webkit-transform: perspective(500px) rotateX(-90deg);
		transform: perspective(500px) rotateX(-90deg);
	}

	100% {
		-webkit-transform-origin: 50% 0;
		transform-origin: 50% 0;
		-webkit-transform: perspective(500px) rotateX(0);
		transform: perspective(500px) rotateX(0);
	}
}

@keyframes rbb_dropdown {
	0% {
		-webkit-transform-origin: 50% 0;
		transform-origin: 50% 0;
		-webkit-transform: perspective(500px) rotateX(-90deg);
		transform: perspective(500px) rotateX(-90deg);
	}

	100% {
		-webkit-transform-origin: 50% 0;
		transform-origin: 50% 0;
		-webkit-transform: perspective(500px) rotateX(0);
		transform: perspective(500px) rotateX(0);
	}
}
