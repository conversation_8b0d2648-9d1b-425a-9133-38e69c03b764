// .................homepage Food 02.................
body {
	&.headphone {
		.site {
			overflow: hidden;
		}

		.title_block {
			.main-title {
				>strong {
					color: var(--rbb-general-primary-color);
				}
			}
		}

		.rbb-main-navigation ul li.level-1 > ul {
			border-top-left-radius: 0;
			border-top-right-radius: 0;
		}

		form.wpcf7-form .footer-form .wpcf7-form-control-wrap input {
			border-radius: 3px;
		}

		form.wpcf7-form .footer-form .button,
		form.wpcf7-form .footer-form .wpcf7-spinner {
			border-radius: 3px;
		}

		.rbb_woo_single_product {
			.product-single__sticky {
				.rbb-single-product-image-for {
					border-radius: 3px;
				}
			}

			.product-single__photos {
				.rbb-single-product-thumbs-content {
					.thumbnail {
						border-radius: 3px;
					}
				}
			}

			.product-summary {
				.quantity {
					.input-text {
						border-radius: 3px;
					}
				}

				.add_to_cart_button,
				.woosw-btn {
					border-radius: 3px;
				}

				.buy-now {
					.add_to_cart_button {
						border-radius: 3px;
					}
				}

			}
		}

		.rbb_woo_products {
			.item-product {
				border-radius: 3px;

				.product__bottom-fade {
					border-radius: 3px;
				}

				.product__bottom .add-to-cart-icon {
					border-radius: 3px;
				}
			}

			@media (max-width: 1440px) {
				>.container {
					max-width: 100%;
				}
			}
		}
	}

	form {
		.footer-form {
			.wpcf7-not-valid-tip {
				color: #fff;
			}
		}
	}
}
