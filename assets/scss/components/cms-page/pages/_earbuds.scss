body {
	&.earbuds {
		.header-3 {
			@media (min-width: 768px) {
				position: absolute;
				top: 0;
				left: 0;
			}

			.header-inner {
				background-color: transparent;
				border-bottom: 1px solid rgba(11, 11, 11, 0.1);
			}

			.rbb-main-navigation {
				.menu-container {
					.level-1 {
						>.a-level {
							&::after {
								content: "";
								position: absolute;
								bottom: 0;
								left: 50%;
								transform: translateX(-50%);
								width: 0%;
								height: 2px;
								background-color: var(--rbb-menu-link-hover-color);
								transition: all 0.35s linear;

							}
						}

						&:hover,
						&.active-parent {
							>.a-level {
								&::after {
									width: 50%;
								}
							}
						}
					}
				}
			}
		}

		.site {
			overflow: hidden;
		}

		.rbb_woo_products {
			&.default {
				.rbb-slick-carousel {
					margin: 0 -20px;
					padding: 0 20px;
				}
			}
		}

		.rbb_woo_single_product {
			.product-single__sticky {
				.rbb-single-product-image-for {
					border: none;
				}
			}
		}

	}

	&:not(.page-template) {
		.header-3 {
			position: relative;
			background-color: var(--rbb-header-background-color);
		}
	}
}
