body {
	&.camera {
		.site {
			overflow: hidden;
		}

		.rbb_woo_single_product {
			.product-single__sticky {
				.rbb-single-product-image-for {
					border-radius: 0;
				}
			}

			.product-single__photos {
				.rbb-single-product-thumbs-content {
					.thumbnail {
						border-radius: 0;
					}
				}
			}

			.product-summary {
				.quantity {
					.input-text {
						border-radius: 0;
					}
				}

				.add_to_cart_button,
				.woosw-btn {
					border-radius: 0;
				}

				.buy-now {
					.add_to_cart_button {
						border-radius: 0;
					}
				}

			}
		}

		.rbb_woo_products {
			.item-product {
				border-radius: 0;

				.product__bottom-fade {
					border-radius: 0;
				}

				.product__bottom .add-to-cart-icon {
					border-radius: 0;
				}
			}

			.rbb-slick-carousel {
				@media (min-width: 1441px) {
					margin: -20px;
					padding: 20px;
				}

				@media (max-width: 1440px) {
					margin: -15px;
					padding: 15px;
				}
			}
		}
	}
}
