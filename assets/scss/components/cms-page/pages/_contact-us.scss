// .............contact-us ...............
.rbb-contact-us {
	form {
		max-width: 770px;

		h2,
		.title {
			display: none;
		}

		.sub_title {
			p {
				text-align: left;
			}
		}

		>p {
			>label {
				&:first-child {
					width: 50%;
					display: inline-block;
					float: left;
					padding-right: 5px;
				}

				&:nth-child(2) {
					width: 50%;
					display: inline-block;
					float: left;
					padding-left: 5px;
				}
			}
		}

		.form-submit {
			display: inline-block;
			min-width: 200px;
		}
	}
}

.hover-icon {
	.elementor-social-icon {
		transition: 0.35s;
		background: var(--rbb-general-primary-color);

		&:hover {
			transition: 0.35s;
			background: var(--rbb-general-secondary-color);
		}
	}
}

#rbb-button-contact {
	text-align: center;

	button {
		min-width: 250px;
		line-height: 48px;
		height: 48px;
		display: inline-block;
		padding: 0 30px;
		border-radius: 4px;
		font-size: 10px;
		color: #fff;
		transition: 0.35s;
		background: var(--rbb-general-primary-color);

		&:hover {
			background: var(--rbb-general-secondary-color);
		}
	}
}

// .........  popup contact..............
#rbb-contact-form {
	.rbb-modal-body {
		background: #fff;
		padding: 40px;
		box-shadow: 0 0 10px #00000040;
		position: relative;
		border-radius: 20px;
		max-width: 500px;
	}
}

form.wpcf7-form {
	h2 {
		text-align: center;
		font-size: 22px;
		color: #222;
		font-weight: 800;
	}

	.sub_title {
		text-align: center;
		color: var(--e-global-color-text);
		line-height: 24px;
		margin-top: 8px;
		margin-bottom: 32px;
		font-size: 12px;
	}

	input {
		padding: 0 22px;
		height: 50px;
		border: 1px solid #d6d6d6;
		border-radius: 4px;
		width: 100%;
		margin-bottom: 10px;
		outline: none !important;

		&::placeholder {
			text-transform: uppercase;
			font-size: 10px;
			color: #a8a8a8;
			font-weight: 600;
		}

		&:focus,
		&:hover {
			border: 1px solid var(--rbb-general-primary-color);
			box-shadow: none;
		}

		&:hover {
			transition: 0.35s;
		}
	}

	textarea {
		padding: 16px 22px;
		border: 1px solid #d6d6d6;
		border-radius: 4px;
		width: 100%;
		max-height: 210px;
		margin-bottom: 20px;
		outline: none !important;

		&::placeholder {
			text-transform: uppercase;
			font-size: 10px;
			color: #a8a8a8;
			font-weight: 600;
		}

		&:focus,
		&:hover {
			border: 1px solid var(--rbb-general-primary-color);
			box-shadow: none;
		}

		&:hover {
			transition: 0.35s;
		}
	}

	.form-submit {
		position: relative;

		p {
			margin-bottom: 0;
		}

		input {
			margin-bottom: 0;
		}
	}

	.wpcf7-submit {
		font-size: 10px;
		color: #fff;
		border-radius: 4px;
		cursor: pointer;
		transition: 0.35s;
		background: var(--rbb-general-primary-color);
		border: 1px solid var(--rbb-general-primary-color);

		&:hover {
			box-shadow: none;
			background: var(--rbb-general-secondary-color);
			border: 1px solid var(--rbb-general-secondary-color);
		}
	}

	.wpcf7-spinner {
		position: absolute;
		bottom: 13px;
		left: 50%;
		margin: 0;
		transform: translateX(-50%);
		text-align: center;
		line-height: 24px;
		background: transparent;

		&::before {
			display: none;
		}

		&::after {
			content: "";
			position: absolute;
			top: 50%;
			left: 50%;
			width: 20px;
			height: 20px;
			background: var(--rbb-button-loading-hover);
			background-size: cover;
			transform: translate(-50%, -50%);
		}
	}

	.wpcf7-not-valid-tip {
		font-size: 11px;
		padding-bottom: 10px;
		position: relative;
		top: -5px;
	}

	.wpcf7-response-output {
		margin: 2em 0 1em;
		min-height: 50px;
		padding: 12px 10px 12px 20px;
		line-height: 25px;
		border: none;
		background-color: #ffdede;
		border-color: #f7c9c9;
		color: #e2401c;
		border-radius: 5px;
		font-weight: 500;
		font-size: 12px;
		z-index: 2;

		&::before {
			content: "";
			font-family: rbb-font, serif !important;
			padding-right: 10px;
		}
	}

	&.submitting {
		.wpcf7-submit {
			transition: 0.35s;
			color: transparent;
		}

		.wpcf7-submit:hover ~ .wpcf7-spinner {
			&::after {
				content: "";
				position: absolute;
				top: 50%;
				left: 50%;
				width: 18px;
				height: 18px;
				background: var(--rbb-button-loading-hover);
				background-size: cover;
				transform: translate(-50%, -50%);
			}
		}
	}
}
