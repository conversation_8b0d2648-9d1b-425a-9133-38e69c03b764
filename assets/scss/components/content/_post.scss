/*
------------Category------------*/
.blog-category {
	.blog-content {
		.blog-wrap {
			.post-thumbnail {
				img {
					transition: 0.7s ease-out;
					transform: scale(1, 1);
					width: 100%;
				}
			}

			&:hover {
				.post-thumbnail {
					img {
						transition: 0.7s ease-out;
						transform: scale(1.05, 1.05);
					}
				}
			}

			.blog-readmore {
				&:hover {
					svg {
						transition: 0.3s;
						fill: var(--rbb-general-primary-color);
					}
				}
			}
		}

		.no-results {
			.page-content {
				text-align: center;

				p {
					font-size: 20px;
				}

				form {
					padding-top: 30px;
					display: inline-block;
					position: relative;

					.search-field {
						height: 50px;
						min-width: 500px;
						border-radius: 50px;
						padding: 0 110px 0 20px;
						border: 2px solid var(--rbb-breadcrumb-background-color);

						&:focus {
							box-shadow: none;
						}
					}

					.search-submit {
						position: absolute;
						right: 0;
						bottom: 0;
						z-index: 10;
						height: 50px;
						padding: 10px 30px;
						color: #fff;
						background: var(--rbb-general-primary-color);
						border-radius: 50px;
						cursor: pointer;
						transition: 0.3s;

						&:hover {
							background: var(--rbb-general-secondary-color);
						}
					}

					.input-group {
						label {
							display: none;
						}

						.input-search {
							height: 50px;
							min-width: 400px;
							border-radius: 5px;
							padding: 10px 50px 10px 20px;

							@media (max-width: 767px) {
								min-width: 290px;
							}

							&:focus {
								box-shadow: none;
								border-color: #6b7280;
							}
						}

						.button-icon {
							top: 12px;
							width: 50px;

							@media (max-width: 767px) {
								top: 19px;
							}

							.search-icon:hover {
								color: var(--rbb-search-icon-color);
							}

							.text-search {
								display: none;
							}
						}
					}
				}
			}
		}
	}
}

/*------------Detail------------*/
body {
	&:not(.elementor-editor-active) {
		.post-content {
			ul {
				margin-left: 40px;

				li {
					position: relative;
					padding: 0 0 12px 20px;

					&::before {
						content: "";
						position: absolute;
						width: 8px;
						height: 8px;
						left: 0;
						top: 9px;
						border-radius: 100%;
						border: 1px solid #bdbdbd;
					}
				}
			}

			ol {
				margin-left: 40px;
				list-style-type: decimal;

				li {
					position: relative;
					padding: 0 0 12px 0;
				}
			}
		}
	}

	.blog-detail {
		@media (max-width: 991px) {
			display: flex;
			flex-direction: column;

			.sidebar-left {
				order: 2;
				padding-top: 50px;
			}

			.sidebar-right {
				padding-top: 50px;
			}
		}

		.blog-content {
			.post-content {
				p {
					margin-bottom: 1rem;
				}
			}
		}

		blockquote {
			padding-left: 105px;
			margin-left: 40px;
			position: relative;

			&::before {
				content: "";
				position: absolute;
				left: 0;
				top: 10px;
				width: 4px;
				height: 90px;
				background: var(--rbb-general-secondary-color);
			}

			&::after {
				content: "";
				position: absolute;
				top: 5px;
				left: 34px;
				width: 44px;
				height: 32px;
				transform: rotateX(180deg);
				background: url(../images/blog/quote.svg) center no-repeat;
				background-size: cover;
			}

			p {
				margin-bottom: 10px;
				font-size: 18px;
				line-height: 36px;
			}

			strong {
				font-weight: 600;
				color: var(--rbb-general-primary-color);
			}
		}

		.post-navigation {
			max-width: 880px;
			margin: 50px auto 0;
		}

		.thumbnail-position-after_title,
		.thumbnail-position-before_title {
			margin-bottom: 30px;
			border-radius: 20px;
		}
	}
}

/*
------------General element------------*/
.rbb-post-categories {
	li {
		list-style: none;
		display: inline-block;

		a {
			color: var(--rbb-general-primary-color);
			background: #fff;
			padding: 8px 20px;
			border-radius: 40px;
			min-height: 36px;
			line-height: 24px;
			font-size: 11px !important;
			transition: 0.3s;
			display: inline-block;
			font-weight: 600;
			margin: 0 10px 10px 0;
			box-shadow: 1px 2px 5px 0 rgb(0 0 0 / 15%);
			text-shadow: 1px 2px 5px rgb(0 0 0 / 15%);

			&:hover {
				color: #fff;
				transition: 0.3s;
				background: var(--rbb-general-button-bg-hover-color);
			}

			@media (max-width: 767px) {
				padding: 5px 15px;
			}
		}
	}
}

.thumbnail-position-on_header {
	background-attachment: fixed;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	margin-bottom: 100px;

	&::after {
		content: "";
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		background-color: #2229;
	}

	.feature-image {
		h1 {
			color: #fff;
		}

		.rbb-post-categories {
			li {
				float: none;
			}
		}
	}
}

// .............. related posts ..........
.related-posts {
	.rbb-related-posts {
		.slick-dots {
			padding-top: 35px;
			position: relative;
		}
	}
}

@media (max-width: 767px) {
	body {
		.blog-detail {
			blockquote {
				padding-left: 30px;
				margin-top: 70px;

				&::after {
					top: -40px;
					left: 50%;
					transform: translateX(-50%) rotate(180deg);
				}
			}
		}
	}
}
