/* Text meant only for screen readers. */
.screen-reader-text {
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute !important;
	width: 1px;
	word-wrap: normal !important; // Many screen reader and browser combinations announce broken words as they would appear visually.

	&:focus {
		background-color: #fff;
		border-radius: 3px;
		box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
		clip: auto !important;
		clip-path: none;
		color: #21759b;
		display: block;
		font-size: 0.875rem;
		font-weight: 700;
		height: auto;
		left: 5px;
		line-height: normal;
		padding: 15px 23px 14px;
		text-decoration: none;
		top: 5px;
		width: auto;
		z-index: 100000; // Above WP toolbar.
	}
}

/* Do not show the outline on the skip link target. */
#primary[tabindex="-1"]:focus {
	outline: 0;
}
.demo_store {
	text-align: center;
	color: #fff;
	.woocommerce-store-notice__dismiss-link {
		font-size: 0;
		position: absolute;
		right: 30px;
		top: 50%;
		margin-top: -9px;
		color: #fff;
		transition: 0.3s;
		&::before {
			content: "✕";
			font-size: 15px;
		}
		&:hover {
			color: var(--rbb-general-secondary-color);
		}
	}
}
