.rbb-tooltip {
	color: #fff;
	background-color: var(--rbb-general-secondary-color);
	padding: 0.5rem;
	font-size: 0.7rem;
	.arrow,
	.arrow::before {
		position: absolute;
		width: 8px;
		height: 8px;
		background: inherit;
	}

	.arrow {
		visibility: hidden;
	}

	.arrow::before {
		visibility: visible;
		content: "";
		transform: rotate(45deg);
	}
	&[data-popper-placement^="top"] > .arrow {
		bottom: -4px;
	}

	&[data-popper-placement^="bottom"] > .arrow {
		top: -4px;
	}

	&[data-popper-placement^="left"] > .arrow {
		right: -4px;
	}

	&[data-popper-placement^="right"] > .arrow {
		left: -4px;
	}
}
