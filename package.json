{"name": "onxen", "title": "onxen", "version": "1.0.0", "homepage": "https://github.com/Abdalsalaam", "config": {"use_pnpm": true, "translate": true, "use_gh_release_notes": true}, "engines": {"node": "^22.14.0"}, "scripts": {"build": "pnpm makepot && pnpm archive", "archive": "composer archive --file=$npm_package_name --format=zip", "makepot": "wpi18n addtextdomain onxen; wpi18n makepot --domain-path languages --pot-file storefront.pot --type theme --exclude node_modules"}, "devDependencies": {"@wordpress/scripts": "^27.4.0", "node-wp-i18n": "~1.2.3"}}